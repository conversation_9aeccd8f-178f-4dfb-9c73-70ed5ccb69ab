📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 0, challengeCompletions: {} }
healthGoalUtils.js:40:10
🎯 Health goal steigereDeineAusdauer active status: false healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: false }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "steigereDeineAusdauer", cardImage: "http://localhost:1234/hg_steigereDeineAusdauer.13b996ae.jpg?1749807540853", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749807540853", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Steigere Deine Ausdauer", healthgoalCoop: "", pillText: "", pillColor: "--primary-brand", … }
healthGoalUtils.js:54:10
🎨 Generated card props: 
Object { cardImage: "http://localhost:1234/hg_steigereDeineAusdauer.13b996ae.jpg?1749807540853", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749807540853", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Steigere Deine Ausdauer", healthgoalCoop: "", pillText: "", pillColor: "--primary-brand", goalinfoIcon1: "http://localhost:1234/icon_moneypig.64d08e58.svg?1749807540853", … }
healthGoalUtils.js:108:10
📊 Progress display: 0 von 0 Challenges geschafft healthGoalUtils.js:109:10
healthgoalsoverview.js loaded healthgoalsOverview.js:303:8
homescreen.js loaded homescreen.js:79:8
HG Fit in deiner Umgebung loaded hg_fitUmgebung.js:194:8
Challenge Lockere Wanderung loaded ch_lockereWanderung.js:444:8
Challenge Gassigehen mit Hund loaded ch_gassiGehen.js:224:8
Challenge Fahrrad-Tour loaded ch_fahrradTour.js:201:8
Challenge Spazieren gehen loaded ch_spazierenGehen.js:225:8
Challenge Plogging loaded ch_plogging.js:225:8
Training Spazieren gehen loaded tr_spazierenGehen.js:120:8
Training Joggen und Müll sammeln (Plogging) loaded tr_plogging.js:122:8
trackerConnect.js loaded trackerConnect.js:242:8
🧪 Health Goal Tests available globally as window.healthGoalTests integrationTestRunner.js:465:10
💡 Try: window.healthGoalTests.quickHealthCheck() integrationTestRunner.js:466:10
🐛 Try: window.healthGoalTests.validateBugFixes() integrationTestRunner.js:467:10
App-Daten aus localStorage geladen utils.js:166:16
Health Goal Progress aus Konfiguration initialisiert utils.js:412:12
Health Goal System initialized healthGoalUtils.js:21:10
Active health goal status: true 
Object { fitUmgebung: true }
healthgoalsOverview.js:295:10
index.js loaded index.js:83:8
DOM geladen, initialisiere Drag-Scrolling index.js:90:10
Initializing chips chip_filterChip.js:58:14
🔍 getHealthGoalWithProgress() called for goalId: fitUmgebung healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749807540853", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749807540853", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749807540853", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 4, challengeCompletions: {…} }
healthGoalUtils.js:40:10
🎯 Health goal fitUmgebung active status: true healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749807540853", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749807540853", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749807540853", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:54:10
🔍 getHealthGoalWithProgress() called for goalId: gesunderSchlaf healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "gesunderSchlaf", cardImage: "http://localhost:1234/people_hugging.d6cb55a3.jpg?1749807540853", tagCategory: "Schlaf", tagIcon: "http://localhost:1234/icon_tag_sleep.a753f5c2.svg?1749807540853", tagColor: "--tag-sleep", tagTextColor: "--tag-text", healthgoalTitle: "Gesunder Schlaf", healthgoalCoop: "", pillText: "", pillColor: "--primary-brand", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 3, challengeCompletions: {} }
healthGoalUtils.js:40:10
🎯 Health goal gesunderSchlaf active status: false healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "gesunderSchlaf", cardImage: "http://localhost:1234/people_hugging.d6cb55a3.jpg?1749807540853", tagCategory: "Schlaf", tagIcon: "http://localhost:1234/icon_tag_sleep.a753f5c2.svg?1749807540853", tagColor: "--tag-sleep", tagTextColor: "--tag-text", healthgoalTitle: "Gesunder Schlaf", healthgoalCoop: "", pillText: "", pillColor: "--primary-brand", … }
healthGoalUtils.js:54:10
🔍 getHealthGoalWithProgress() called for goalId: aktivInDerNatur healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "aktivInDerNatur", cardImage: "http://localhost:1234/hg_aktivInDerNatur.f89b0946.jpg?1749807540853", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749807540853", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Aktiv in der Natur", healthgoalCoop: "", pillText: "", pillColor: "--primary-brand", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 5, challengeCompletions: {} }
healthGoalUtils.js:40:10
🎯 Health goal aktivInDerNatur active status: false healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "aktivInDerNatur", cardImage: "http://localhost:1234/hg_aktivInDerNatur.f89b0946.jpg?1749807540853", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749807540853", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Aktiv in der Natur", healthgoalCoop: "", pillText: "", pillColor: "--primary-brand", … }
healthGoalUtils.js:54:10
🔍 getHealthGoalWithProgress() called for goalId: issDichGesund healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "issDichGesund", cardImage: "http://localhost:1234/hg_issDichGesund.86f47dba.jpg?1749807540853", tagCategory: "Ernährung", tagIcon: "http://localhost:1234/icon_tag_food.24dde4ed.svg?1749807540853", tagColor: "--tag-food", tagTextColor: "--tag-text", healthgoalTitle: "Iss Dich gesund", healthgoalCoop: "", pillText: "", pillColor: "--primary-brand", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 4, challengeCompletions: {} }
healthGoalUtils.js:40:10
🎯 Health goal issDichGesund active status: false healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "issDichGesund", cardImage: "http://localhost:1234/hg_issDichGesund.86f47dba.jpg?1749807540853", tagCategory: "Ernährung", tagIcon: "http://localhost:1234/icon_tag_food.24dde4ed.svg?1749807540853", tagColor: "--tag-food", tagTextColor: "--tag-text", healthgoalTitle: "Iss Dich gesund", healthgoalCoop: "", pillText: "", pillColor: "--primary-brand", … }
healthGoalUtils.js:54:10
🔍 getHealthGoalWithProgress() called for goalId: mehrBewegungImAlltag healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "mehrBewegungImAlltag", cardImage: "http://localhost:1234/hg_mehrBewegungImAlltag.3d9f9a77.jpg?1749807540853", cardImageInactive: "http://localhost:1234/cyberfitness.49ccfaf3.jpg?1749807540853", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749807540853", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Mehr Bewegung im Alltag", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Noch 7 Tage verfügbar", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 6, challengeCompletions: {} }
healthGoalUtils.js:40:10
🎯 Health goal mehrBewegungImAlltag active status: false healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "mehrBewegungImAlltag", cardImage: "http://localhost:1234/hg_mehrBewegungImAlltag.3d9f9a77.jpg?1749807540853", cardImageInactive: "http://localhost:1234/cyberfitness.49ccfaf3.jpg?1749807540853", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749807540853", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Mehr Bewegung im Alltag", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Noch 7 Tage verfügbar", … }
healthGoalUtils.js:54:10
🔍 getHealthGoalWithProgress() called for goalId: mehrGelassenheit healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "mehrGelassenheit", cardImage: "http://localhost:1234/hg_mehrGelassenheit.da8caa57.jpg?1749807540853", tagCategory: "Psyche", tagIcon: "http://localhost:1234/icon_tag_brain.b39a0624.svg?1749807540853", tagColor: "--tag-psych", tagTextColor: "--tag-text", healthgoalTitle: "Finde zu mehr Gelassenheit", healthgoalCoop: "", pillText: "", pillColor: "--primary-brand", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 4, challengeCompletions: {} }
healthGoalUtils.js:40:10
🎯 Health goal mehrGelassenheit active status: false healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "mehrGelassenheit", cardImage: "http://localhost:1234/hg_mehrGelassenheit.da8caa57.jpg?1749807540853", tagCategory: "Psyche", tagIcon: "http://localhost:1234/icon_tag_brain.b39a0624.svg?1749807540853", tagColor: "--tag-psych", tagTextColor: "--tag-text", healthgoalTitle: "Finde zu mehr Gelassenheit", healthgoalCoop: "", pillText: "", pillColor: "--primary-brand", … }
healthGoalUtils.js:54:10
🔍 getHealthGoalWithProgress() called for goalId: staerkeDeineMuskeln healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "staerkeDeineMuskeln", cardImage: "http://localhost:1234/hg_staerkeDeineMuskeln.4fc55ac5.jpg?1749807540853", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749807540853", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Stärke Deine Muskeln", healthgoalCoop: "", pillText: "", pillColor: "--primary-brand", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 5, challengeCompletions: {} }
healthGoalUtils.js:40:10
🎯 Health goal staerkeDeineMuskeln active status: false healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "staerkeDeineMuskeln", cardImage: "http://localhost:1234/hg_staerkeDeineMuskeln.4fc55ac5.jpg?1749807540853", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749807540853", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Stärke Deine Muskeln", healthgoalCoop: "", pillText: "", pillColor: "--primary-brand", … }
healthGoalUtils.js:54:10
🔍 getHealthGoalWithProgress() called for goalId: steigereDeineAusdauer healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "steigereDeineAusdauer", cardImage: "http://localhost:1234/hg_steigereDeineAusdauer.13b996ae.jpg?1749807540853", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749807540853", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Steigere Deine Ausdauer", healthgoalCoop: "", pillText: "", pillColor: "--primary-brand", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 4, challengeCompletions: {} }
healthGoalUtils.js:40:10
🎯 Health goal steigereDeineAusdauer active status: false healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "steigereDeineAusdauer", cardImage: "http://localhost:1234/hg_steigereDeineAusdauer.13b996ae.jpg?1749807540853", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749807540853", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Steigere Deine Ausdauer", healthgoalCoop: "", pillText: "", pillColor: "--primary-brand", … }
healthGoalUtils.js:54:10
Initializing segmented control segmentedControl.js:38:14
First Tab found, clicking segmentedControl.js:41:16
[/healthgoals-overview] SegmentedControl initialisiert router.js:347:14