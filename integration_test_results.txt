App loaded hg-fitUmgebung:47:17
[App INFO]: Willkommens-Modul geladen utils.js:40:14
Segmented control module loaded segmentedControl.js:109:8
Chips module loaded chip_filterChip.js:66:8
🔄 Calling dynamic content function for segment: Segment1 segmentedControl.js:36:14
🔄 healthgoalOverview_segment1Content() called - generating fresh template healthgoalOverview_Segment1.js:119:10
🔍 === DATA FLOW VERIFICATION START === healthGoalUtils.js:366:10
🎯 Tracing data flow for health goal: fitUmgebung healthGoalUtils.js:367:10
📊 Step 1: Checking appStorage raw data healthGoalUtils.js:376:10
Raw appStorage data: 
Object { activeHealthGoals: {…}, healthGoalProgress: {…}, activeHealthGoalsGetter: {…} }
healthGoalUtils.js:382:10
📊 Step 2: Testing getActiveHealthGoal() healthGoalUtils.js:385:10
🔍 getActiveHealthGoal() called healthGoalUtils.js:119:10
📊 appStorage.activeHealthGoals: 
Object { fitUmgebung: false }
healthGoalUtils.js:120:10
📊 appStorage._data.activeHealthGoals: 
Object { fitUmgebung: false }
healthGoalUtils.js:121:10
🎯 Filtered active goals: 
Array []
healthGoalUtils.js:126:10
❌ No active health goals found healthGoalUtils.js:129:12
Active health goal result: null healthGoalUtils.js:388:10
📊 Step 3: Testing getHealthGoalWithProgress() healthGoalUtils.js:391:10
🔍 getHealthGoalWithProgress() called for goalId: fitUmgebung healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749808680609", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808680609", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749808680609", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 4, challengeCompletions: {} }
healthGoalUtils.js:40:10
🎯 Health goal fitUmgebung active status: false healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: false }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749808680609", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808680609", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749808680609", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:54:10
Health goal with progress: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749808680609", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808680609", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749808680609", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:394:10
📊 Step 4: Testing getHealthGoalCardProps() healthGoalUtils.js:397:10
🎨 getHealthGoalCardProps() called for goalId: fitUmgebung, isActiveCard: true healthGoalUtils.js:73:10
🔍 getHealthGoalWithProgress() called for goalId: fitUmgebung healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749808680609", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808680609", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749808680609", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 4, challengeCompletions: {} }
healthGoalUtils.js:40:10
🎯 Health goal fitUmgebung active status: false healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: false }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749808680609", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808680609", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749808680609", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:54:10
🎨 Generated card props: 
Object { cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808680609", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749808680609", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", pillColor: "--accent-blue", goalinfoIcon1: "http://localhost:1234/icon_moneypig.64d08e58.svg?1749808680609", … }
healthGoalUtils.js:108:10
📊 Progress display: 0 von 4 Challenges geschafft healthGoalUtils.js:109:10
Card props for active card: 
Object { cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808680609", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749808680609", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", pillColor: "--accent-blue", goalinfoIcon1: "http://localhost:1234/icon_moneypig.64d08e58.svg?1749808680609", … }
healthGoalUtils.js:400:10
📊 Step 5: Checking configuration healthGoalUtils.js:403:10
Health goal configuration: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749808680609", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808680609", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749808680609", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:406:10
📊 Step 6: Validating data consistency healthGoalUtils.js:409:10
Data consistency validation: 
Object { hasActiveGoal: false, activeGoalMatchesTarget: false, hasProgress: true, hasCardProps: true, hasConfig: true, progressDataConsistent: true }
healthGoalUtils.js:419:10
🔍 === DATA FLOW VERIFICATION SUMMARY === healthGoalUtils.js:431:10
✅ Data flow working: false healthGoalUtils.js:432:10
📊 Progress display: 0 von 4 Challenges geschafft healthGoalUtils.js:433:10
❌ Issues found: hasActiveGoal, activeGoalMatchesTarget healthGoalUtils.js:435:12
🔍 === DATA FLOW VERIFICATION END === healthGoalUtils.js:437:10
🔄 generateDynamicContent() called - checking for active health goal healthgoalOverview_Segment1.js:33:12
🔍 getActiveHealthGoal() called healthGoalUtils.js:119:10
📊 appStorage.activeHealthGoals: 
Object { fitUmgebung: false }
healthGoalUtils.js:120:10
📊 appStorage._data.activeHealthGoals: 
Object { fitUmgebung: false }
healthGoalUtils.js:121:10
🎯 Filtered active goals: 
Array []
healthGoalUtils.js:126:10
❌ No active health goals found healthGoalUtils.js:129:12
📊 Active health goal result: null healthgoalOverview_Segment1.js:37:12
🔍 getHealthGoalWithProgress() called for goalId: fitUmgebung healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749808680609", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808680609", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749808680609", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 4, challengeCompletions: {} }
healthGoalUtils.js:40:10
🎯 Health goal fitUmgebung active status: false healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: false }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749808680609", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808680609", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749808680609", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:54:10
🔍 getHealthGoalWithProgress() called for goalId: gesunderSchlaf healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "gesunderSchlaf", cardImage: "http://localhost:1234/people_hugging.d6cb55a3.jpg?1749808680609", tagCategory: "Schlaf", tagIcon: "http://localhost:1234/icon_tag_sleep.a753f5c2.svg?1749808680609", tagColor: "--tag-sleep", tagTextColor: "--tag-text", healthgoalTitle: "Gesunder Schlaf", healthgoalCoop: "", pillText: "", pillColor: "--primary-brand", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 0, challengeCompletions: {} }
healthGoalUtils.js:40:10
🎯 Health goal gesunderSchlaf active status: false healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: false }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "gesunderSchlaf", cardImage: "http://localhost:1234/people_hugging.d6cb55a3.jpg?1749808680609", tagCategory: "Schlaf", tagIcon: "http://localhost:1234/icon_tag_sleep.a753f5c2.svg?1749808680609", tagColor: "--tag-sleep", tagTextColor: "--tag-text", healthgoalTitle: "Gesunder Schlaf", healthgoalCoop: "", pillText: "", pillColor: "--primary-brand", … }
healthGoalUtils.js:54:10
🔍 getHealthGoalWithProgress() called for goalId: aktivInDerNatur healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "aktivInDerNatur", cardImage: "http://localhost:1234/hg_aktivInDerNatur.f89b0946.jpg?1749808680609", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749808680609", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Aktiv in der Natur", healthgoalCoop: "", pillText: "", pillColor: "--primary-brand", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 0, challengeCompletions: {} }
healthGoalUtils.js:40:10
🎯 Health goal aktivInDerNatur active status: false healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: false }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "aktivInDerNatur", cardImage: "http://localhost:1234/hg_aktivInDerNatur.f89b0946.jpg?1749808680609", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749808680609", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Aktiv in der Natur", healthgoalCoop: "", pillText: "", pillColor: "--primary-brand", … }
healthGoalUtils.js:54:10
🔍 getHealthGoalWithProgress() called for goalId: issDichGesund healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "issDichGesund", cardImage: "http://localhost:1234/hg_issDichGesund.86f47dba.jpg?1749808680609", tagCategory: "Ernährung", tagIcon: "http://localhost:1234/icon_tag_food.24dde4ed.svg?1749808680609", tagColor: "--tag-food", tagTextColor: "--tag-text", healthgoalTitle: "Iss Dich gesund", healthgoalCoop: "", pillText: "", pillColor: "--primary-brand", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 0, challengeCompletions: {} }
healthGoalUtils.js:40:10
🎯 Health goal issDichGesund active status: false healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: false }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "issDichGesund", cardImage: "http://localhost:1234/hg_issDichGesund.86f47dba.jpg?1749808680609", tagCategory: "Ernährung", tagIcon: "http://localhost:1234/icon_tag_food.24dde4ed.svg?1749808680609", tagColor: "--tag-food", tagTextColor: "--tag-text", healthgoalTitle: "Iss Dich gesund", healthgoalCoop: "", pillText: "", pillColor: "--primary-brand", … }
healthGoalUtils.js:54:10
🔍 getHealthGoalWithProgress() called for goalId: mehrBewegungImAlltag healthGoalUtils.js:30:10
