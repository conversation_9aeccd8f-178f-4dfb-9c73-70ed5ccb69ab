/**
 * Integration Test Runner for Dynamic Health Goals System
 * 
 * This module provides comprehensive testing capabilities for the health goal system.
 * It can be run from the browser console to validate all components are working correctly.
 */

import { 
  runIntegrationTests, 
  testCompleteFlow, 
  verifyDataFlowConnections,
  validateHealthGoalSystem,
  resetHealthGoalProgress,
  activateHealthGoal,
  getActiveHealthGoal
} from './healthGoalUtils.js';

import { 
  recordTrainingCompletion, 
  getChallengeStatus, 
  startChallenge,
  getHealthGoalCompletionSummary 
} from './challengeCompletionUtils.js';

import { appStorage } from '../../utils.js';

/**
 * Main test runner - executes all tests and provides comprehensive report
 * @param {boolean} verbose - Whether to show detailed logging
 * @returns {Object} Complete test results
 */
export const runAllTests = (verbose = true) => {
  if (verbose) {
    console.log('🚀 === DYNAMIC HEALTH GOALS SYSTEM - FULL INTEGRATION TEST ===');
    console.log('🕐 Started at:', new Date().toISOString());
  }
  
  const testSuite = {
    timestamp: new Date().toISOString(),
    systemInfo: {
      userAgent: navigator.userAgent,
      url: window.location.href,
      appStorageInitialized: !!appStorage._data
    },
    results: {},
    summary: {
      totalTests: 0,
      passed: 0,
      failed: 0,
      warnings: 0
    }
  };
  
  try {
    // Test 1: System Validation
    if (verbose) console.log('\n🔍 Running System Validation...');
    testSuite.results.systemValidation = validateHealthGoalSystem();
    testSuite.summary.totalTests++;
    if (testSuite.results.systemValidation.valid) {
      testSuite.summary.passed++;
    } else {
      testSuite.summary.failed++;
    }
    
    // Test 2: Data Flow Verification
    if (verbose) console.log('\n🔄 Running Data Flow Verification...');
    testSuite.results.dataFlowVerification = verifyDataFlowConnections('fitUmgebung');
    testSuite.summary.totalTests++;
    if (testSuite.results.dataFlowVerification.summary.isDataFlowWorking) {
      testSuite.summary.passed++;
    } else {
      testSuite.summary.failed++;
    }
    
    // Test 3: Integration Tests
    if (verbose) console.log('\n🧪 Running Integration Tests...');
    testSuite.results.integrationTests = runIntegrationTests();
    testSuite.summary.totalTests += testSuite.results.integrationTests.summary.total;
    testSuite.summary.passed += testSuite.results.integrationTests.summary.passed;
    testSuite.summary.failed += testSuite.results.integrationTests.summary.failed;
    
    // Test 4: Complete Flow Test
    if (verbose) console.log('\n🔄 Running Complete Flow Test...');
    testSuite.results.completeFlowTest = testCompleteFlow();
    testSuite.summary.totalTests++;
    if (testSuite.results.completeFlowTest.success) {
      testSuite.summary.passed++;
    } else {
      testSuite.summary.failed++;
    }
    
    // Test 5: Challenge System Test
    if (verbose) console.log('\n⚔️ Running Challenge System Test...');
    testSuite.results.challengeSystemTest = testChallengeSystem();
    testSuite.summary.totalTests++;
    if (testSuite.results.challengeSystemTest.success) {
      testSuite.summary.passed++;
    } else {
      testSuite.summary.failed++;
    }
    
    // Test 6: UI Integration Test
    if (verbose) console.log('\n🎨 Running UI Integration Test...');
    testSuite.results.uiIntegrationTest = testUIIntegration();
    testSuite.summary.totalTests++;
    if (testSuite.results.uiIntegrationTest.success) {
      testSuite.summary.passed++;
    } else {
      testSuite.summary.failed++;
    }
    
  } catch (error) {
    testSuite.results.criticalError = {
      error: error.message,
      stack: error.stack
    };
    testSuite.summary.failed++;
  }
  
  // Calculate success rate
  testSuite.summary.successRate = testSuite.summary.totalTests > 0 ? 
    Math.round((testSuite.summary.passed / testSuite.summary.totalTests) * 100) : 0;
  
  // Final report
  if (verbose) {
    console.log('\n🚀 === FINAL TEST REPORT ===');
    console.log(`📊 Total Tests: ${testSuite.summary.totalTests}`);
    console.log(`✅ Passed: ${testSuite.summary.passed}`);
    console.log(`❌ Failed: ${testSuite.summary.failed}`);
    console.log(`⚠️ Warnings: ${testSuite.summary.warnings}`);
    console.log(`📈 Success Rate: ${testSuite.summary.successRate}%`);
    
    if (testSuite.summary.successRate >= 90) {
      console.log('🎉 EXCELLENT! System is working correctly.');
    } else if (testSuite.summary.successRate >= 70) {
      console.log('⚠️ GOOD but needs attention. Some issues found.');
    } else {
      console.log('❌ CRITICAL ISSUES found. System needs fixes.');
    }
    
    console.log('🕐 Completed at:', new Date().toISOString());
    console.log('🚀 === END OF TEST REPORT ===\n');
  }
  
  return testSuite;
};

/**
 * Test the challenge system functionality
 * @returns {Object} Challenge system test results
 */
const testChallengeSystem = () => {
  const test = {
    timestamp: new Date().toISOString(),
    success: true,
    errors: [],
    details: {}
  };
  
  try {
    // Test challenge status retrieval
    const challengeStatus = getChallengeStatus('lockereWanderung');
    test.details.challengeStatus = challengeStatus;
    
    if (!challengeStatus.found) {
      test.success = false;
      test.errors.push('Challenge lockereWanderung not found');
    }
    
    // Test health goal completion summary
    const completionSummary = getHealthGoalCompletionSummary('fitUmgebung');
    test.details.completionSummary = completionSummary;
    
    if (!completionSummary.healthGoalId) {
      test.success = false;
      test.errors.push('Health goal completion summary failed');
    }
    
  } catch (error) {
    test.success = false;
    test.errors.push(`Challenge system error: ${error.message}`);
  }
  
  return test;
};

/**
 * Test UI integration and template generation
 * @returns {Object} UI integration test results
 */
const testUIIntegration = () => {
  const test = {
    timestamp: new Date().toISOString(),
    success: true,
    errors: [],
    details: {}
  };
  
  try {
    // Test if health goal is active
    const activeGoal = getActiveHealthGoal();
    test.details.activeGoal = activeGoal;
    
    if (!activeGoal) {
      test.success = false;
      test.errors.push('No active health goal for UI display');
    }
    
    // Test DOM elements (if available)
    const tabContent = document.querySelector('.tabcontent');
    test.details.domElements = {
      tabContentExists: !!tabContent,
      tabContentStyle: tabContent ? getComputedStyle(tabContent).flexDirection : null
    };
    
    if (tabContent && getComputedStyle(tabContent).flexDirection !== 'column') {
      test.errors.push('CSS layout fix not applied - flex-direction should be column');
    }
    
  } catch (error) {
    test.success = false;
    test.errors.push(`UI integration error: ${error.message}`);
  }
  
  return test;
};

/**
 * Quick health check - minimal test for basic functionality
 * @returns {boolean} True if basic functionality is working
 */
export const quickHealthCheck = () => {
  try {
    console.log('🏥 Quick Health Check...');
    
    // Check if appStorage is working
    const storageWorking = !!appStorage._data;
    console.log(`📊 AppStorage: ${storageWorking ? '✅' : '❌'}`);
    
    // Check if health goal can be activated
    activateHealthGoal('fitUmgebung');
    const activationWorking = appStorage.activeHealthGoals.fitUmgebung;
    console.log(`🎯 Health Goal Activation: ${activationWorking ? '✅' : '❌'}`);
    
    // Check if active goal can be retrieved
    const activeGoal = getActiveHealthGoal();
    const retrievalWorking = !!activeGoal;
    console.log(`🔍 Active Goal Retrieval: ${retrievalWorking ? '✅' : '❌'}`);
    
    const allWorking = storageWorking && activationWorking && retrievalWorking;
    console.log(`🏥 Overall Health: ${allWorking ? '✅ HEALTHY' : '❌ NEEDS ATTENTION'}`);
    
    return allWorking;
  } catch (error) {
    console.log('❌ Health Check Failed:', error.message);
    return false;
  }
};

/**
 * Demo function to show the complete system working
 * @returns {Object} Demo results
 */
export const runDemo = () => {
  console.log('🎬 === DYNAMIC HEALTH GOALS SYSTEM DEMO ===');
  
  const demo = {
    timestamp: new Date().toISOString(),
    steps: []
  };
  
  try {
    // Step 1: Reset system
    console.log('🔄 Step 1: Resetting system...');
    resetHealthGoalProgress(false);
    demo.steps.push('System reset');
    
    // Step 2: Activate health goal
    console.log('🎯 Step 2: Activating health goal...');
    activateHealthGoal('fitUmgebung');
    demo.steps.push('Health goal activated');
    
    // Step 3: Show progress
    console.log('📊 Step 3: Showing initial progress...');
    const activeGoal = getActiveHealthGoal();
    console.log(`Active Goal: ${activeGoal?.healthgoalTitle}`);
    console.log(`Progress: ${activeGoal?.completedChallenges} von ${activeGoal?.totalChallenges} Challenges geschafft`);
    demo.steps.push('Progress displayed');
    
    // Step 4: Simulate training completion
    console.log('⚔️ Step 4: Simulating training completion...');
    try {
      const trainingResult = recordTrainingCompletion('lockereWanderung', 1);
      console.log(`Training result:`, trainingResult);
      demo.steps.push('Training completion simulated');
    } catch (error) {
      console.log('⚠️ Training completion simulation failed (expected in test environment)');
      demo.steps.push('Training completion simulation attempted');
    }
    
    console.log('🎬 Demo completed successfully!');
    
  } catch (error) {
    console.log('❌ Demo failed:', error.message);
    demo.error = error.message;
  }
  
  return demo;
};

/**
 * Test the specific issues mentioned in the original bug report
 * @returns {Object} Bug fix validation results
 */
export const validateBugFixes = () => {
  console.log('🐛 === BUG FIX VALIDATION ===');

  const validation = {
    timestamp: new Date().toISOString(),
    issues: {},
    summary: { fixed: 0, total: 4 }
  };

  // Issue 1: Active Health Goal Detection Bug
  console.log('🔍 Testing Issue 1: Active Health Goal Detection Bug');
  try {
    // Ensure health goal is activated
    activateHealthGoal('fitUmgebung');

    // Test if getActiveHealthGoal() detects it
    const activeGoal = getActiveHealthGoal();
    const issue1Fixed = activeGoal !== null && activeGoal.id === 'fitUmgebung';

    validation.issues.activeHealthGoalDetection = {
      fixed: issue1Fixed,
      details: {
        appStorageState: appStorage.activeHealthGoals,
        detectedActiveGoal: activeGoal,
        expectedGoalId: 'fitUmgebung'
      }
    };

    if (issue1Fixed) validation.summary.fixed++;
    console.log(`Issue 1: ${issue1Fixed ? '✅ FIXED' : '❌ NOT FIXED'}`);

  } catch (error) {
    validation.issues.activeHealthGoalDetection = {
      fixed: false,
      error: error.message
    };
    console.log('❌ Issue 1: ERROR -', error.message);
  }

  // Issue 2: Health Goal Activation After Niveau-Poll
  console.log('🔍 Testing Issue 2: Health Goal Activation After Niveau-Poll');
  try {
    // Reset and test activation flow
    resetHealthGoalProgress(false);

    // Simulate niveau-poll completion
    appStorage.setHealthGoalLevel('beginner');
    appStorage.activateHealthGoal('fitUmgebung');

    const activatedAfterPoll = appStorage.activeHealthGoals.fitUmgebung;
    const issue2Fixed = activatedAfterPoll === true;

    validation.issues.niveauPollActivation = {
      fixed: issue2Fixed,
      details: {
        healthGoalLevel: appStorage._data.healthGoalLevel,
        activatedAfterPoll
      }
    };

    if (issue2Fixed) validation.summary.fixed++;
    console.log(`Issue 2: ${issue2Fixed ? '✅ FIXED' : '❌ NOT FIXED'}`);

  } catch (error) {
    validation.issues.niveauPollActivation = {
      fixed: false,
      error: error.message
    };
    console.log('❌ Issue 2: ERROR -', error.message);
  }

  // Issue 3: Data Connection Verification
  console.log('🔍 Testing Issue 3: Data Connection Verification');
  try {
    const dataFlowResult = verifyDataFlowConnections('fitUmgebung');
    const issue3Fixed = dataFlowResult.summary.isDataFlowWorking;

    validation.issues.dataConnections = {
      fixed: issue3Fixed,
      details: dataFlowResult.summary
    };

    if (issue3Fixed) validation.summary.fixed++;
    console.log(`Issue 3: ${issue3Fixed ? '✅ FIXED' : '❌ NOT FIXED'}`);

  } catch (error) {
    validation.issues.dataConnections = {
      fixed: false,
      error: error.message
    };
    console.log('❌ Issue 3: ERROR -', error.message);
  }

  // Issue 4: CSS Layout Fix
  console.log('🔍 Testing Issue 4: CSS Layout Fix');
  try {
    const tabContent = document.querySelector('.tabcontent');
    const flexDirection = tabContent ? getComputedStyle(tabContent).flexDirection : null;
    const issue4Fixed = flexDirection === 'column';

    validation.issues.cssLayout = {
      fixed: issue4Fixed,
      details: {
        tabContentExists: !!tabContent,
        flexDirection,
        expectedFlexDirection: 'column'
      }
    };

    if (issue4Fixed) validation.summary.fixed++;
    console.log(`Issue 4: ${issue4Fixed ? '✅ FIXED' : '❌ NOT FIXED'}`);

  } catch (error) {
    validation.issues.cssLayout = {
      fixed: false,
      error: error.message
    };
    console.log('❌ Issue 4: ERROR -', error.message);
  }

  // Summary
  const successRate = Math.round((validation.summary.fixed / validation.summary.total) * 100);
  validation.summary.successRate = successRate;

  console.log('🐛 === BUG FIX VALIDATION SUMMARY ===');
  console.log(`✅ Fixed: ${validation.summary.fixed}/${validation.summary.total}`);
  console.log(`📊 Success Rate: ${successRate}%`);

  if (successRate === 100) {
    console.log('🎉 ALL BUGS FIXED! System is working correctly.');
  } else if (successRate >= 75) {
    console.log('⚠️ Most bugs fixed, but some issues remain.');
  } else {
    console.log('❌ Critical bugs still present. System needs more work.');
  }

  return validation;
};

// Make functions available globally for console testing
if (typeof window !== 'undefined') {
  window.healthGoalTests = {
    runAllTests,
    quickHealthCheck,
    runDemo,
    validateBugFixes,
    verifyDataFlowConnections,
    validateHealthGoalSystem
  };

  console.log('🧪 Health Goal Tests available globally as window.healthGoalTests');
  console.log('💡 Try: window.healthGoalTests.quickHealthCheck()');
  console.log('🐛 Try: window.healthGoalTests.validateBugFixes()');
}
