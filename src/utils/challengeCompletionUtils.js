/**
 * Challenge Completion Utilities
 * 
 * This module provides utilities for tracking challenge and training completion
 * across the health goal system. It implements the 80% completion rule and
 * manages the relationship between trainings, challenges, and health goals.
 */

import { appStorage } from '../../utils.js';
import { updateHealthGoalProgress } from './healthGoalUtils.js';

/**
 * Challenge configuration mapping
 * Maps challenge names to their health goal and training requirements
 */
export const challengeConfig = {
  'lockereWanderung': {
    healthGoalId: 'fitUmgebung',
    totalTrainings: 2,
    displayName: 'Lockere Wanderung'
  },
  'spazierenGehen': {
    healthGoalId: 'fitUmgebung',
    totalTrainings: 1,
    displayName: 'Spazieren gehen'
  },
  'gassiGehen': {
    healthGoalId: 'fitUmgebung',
    totalTrainings: 1,
    displayName: 'Gassi gehen'
  },
  'fahrradTour': {
    healthGoalId: 'fitUmgebung',
    totalTrainings: 2,
    displayName: 'Fahrrad-Tour'
  },
  'plogging': {
    healthGoalId: 'fitUmgebung',
    totalTrainings: 1,
    displayName: 'Plogging'
  }
  // Add more challenges as they are implemented
};

/**
 * Record a training completion for a challenge
 * @param {string} challengeName - Name of the challenge
 * @param {number} additionalTrainings - Number of additional trainings completed (default: 1)
 * @returns {Object} Result object with completion status and progress
 */
export const recordTrainingCompletion = (challengeName, additionalTrainings = 1) => {
  const config = challengeConfig[challengeName];
  if (!config) {
    console.error(`Challenge ${challengeName} not found in configuration`);
    return { success: false, error: 'Challenge not found' };
  }

  // Get current challenge data from old system
  const currentChallenge = appStorage._data.challenges?.[challengeName];
  if (!currentChallenge || !currentChallenge.started) {
    console.error(`Challenge ${challengeName} not started`);
    return { success: false, error: 'Challenge not started' };
  }

  const currentCompletedTrainings = currentChallenge.completedTrainings || 0;
  const totalTrainings = config.totalTrainings;
  const newCompletedTrainings = Math.min(currentCompletedTrainings + additionalTrainings, totalTrainings);

  // Check if already completed
  if (currentCompletedTrainings >= totalTrainings) {
    console.log(`Challenge ${challengeName} already completed`);
    return { 
      success: true, 
      alreadyCompleted: true,
      completedTrainings: currentCompletedTrainings,
      totalTrainings
    };
  }

  // Update old challenge system
  appStorage.updateChallenge(challengeName, {
    completedTrainings: newCompletedTrainings
  });

  // Update new health goal progress system
  updateHealthGoalProgress(config.healthGoalId, challengeName, newCompletedTrainings, totalTrainings);

  // Calculate completion percentage
  const completionPercentage = (newCompletedTrainings / totalTrainings) * 100;
  const challengeCompleted = completionPercentage >= 80;

  console.log(`Training recorded for ${challengeName}: ${newCompletedTrainings}/${totalTrainings} (${completionPercentage.toFixed(1)}%)`);

  if (challengeCompleted) {
    console.log(`🎉 Challenge ${challengeName} completed! (≥80% rule met)`);
  }

  return {
    success: true,
    challengeName,
    completedTrainings: newCompletedTrainings,
    totalTrainings,
    completionPercentage,
    challengeCompleted,
    wasAlreadyCompleted: false
  };
};

/**
 * Get challenge completion status
 * @param {string} challengeName - Name of the challenge
 * @returns {Object} Challenge status information
 */
export const getChallengeStatus = (challengeName) => {
  const config = challengeConfig[challengeName];
  if (!config) {
    return { found: false, error: 'Challenge not found' };
  }

  const currentChallenge = appStorage._data.challenges?.[challengeName];
  const started = currentChallenge?.started || false;
  const completedTrainings = currentChallenge?.completedTrainings || 0;
  const totalTrainings = config.totalTrainings;
  const completionPercentage = totalTrainings > 0 ? (completedTrainings / totalTrainings) * 100 : 0;
  const challengeCompleted = completionPercentage >= 80;

  // Also check new health goal progress system
  const healthGoalProgress = appStorage.getHealthGoalProgress(config.healthGoalId);
  const challengeCompletionData = healthGoalProgress.challengeCompletions[challengeName];
  const newSystemCompleted = challengeCompletionData?.completed || false;

  return {
    found: true,
    challengeName,
    displayName: config.displayName,
    healthGoalId: config.healthGoalId,
    started,
    completedTrainings,
    totalTrainings,
    completionPercentage,
    challengeCompleted: challengeCompleted || newSystemCompleted,
    newSystemData: challengeCompletionData
  };
};

/**
 * Start a challenge
 * @param {string} challengeName - Name of the challenge to start
 * @returns {Object} Result object
 */
export const startChallenge = (challengeName) => {
  const config = challengeConfig[challengeName];
  if (!config) {
    console.error(`Challenge ${challengeName} not found in configuration`);
    return { success: false, error: 'Challenge not found' };
  }

  // Initialize challenge in old system
  appStorage.updateChallenge(challengeName, {
    started: true,
    completedTrainings: 0,
    totalTrainings: config.totalTrainings
  });

  // Initialize in new health goal progress system
  updateHealthGoalProgress(config.healthGoalId, challengeName, 0, config.totalTrainings);

  console.log(`Challenge ${challengeName} started`);

  return {
    success: true,
    challengeName,
    displayName: config.displayName,
    totalTrainings: config.totalTrainings
  };
};

/**
 * Get all challenges for a health goal
 * @param {string} healthGoalId - Health goal ID
 * @returns {Array} Array of challenge status objects
 */
export const getChallengesForHealthGoal = (healthGoalId) => {
  return Object.keys(challengeConfig)
    .filter(challengeName => challengeConfig[challengeName].healthGoalId === healthGoalId)
    .map(challengeName => getChallengeStatus(challengeName));
};

/**
 * Get overall health goal completion status
 * @param {string} healthGoalId - Health goal ID
 * @returns {Object} Health goal completion summary
 */
export const getHealthGoalCompletionSummary = (healthGoalId) => {
  const challenges = getChallengesForHealthGoal(healthGoalId);
  const totalChallenges = challenges.length;
  const completedChallenges = challenges.filter(c => c.challengeCompleted).length;
  const startedChallenges = challenges.filter(c => c.started).length;
  
  const healthGoalProgress = appStorage.getHealthGoalProgress(healthGoalId);
  
  return {
    healthGoalId,
    totalChallenges,
    completedChallenges: Math.max(completedChallenges, healthGoalProgress.completedChallenges),
    startedChallenges,
    challenges,
    completionPercentage: totalChallenges > 0 ? (completedChallenges / totalChallenges) * 100 : 0,
    newSystemData: healthGoalProgress
  };
};

/**
 * Reset all challenge progress for a health goal
 * @param {string} healthGoalId - Health goal ID
 */
export const resetHealthGoalChallenges = (healthGoalId) => {
  const challenges = getChallengesForHealthGoal(healthGoalId);
  
  challenges.forEach(challenge => {
    if (challenge.found) {
      // Reset old system
      appStorage.updateChallenge(challenge.challengeName, {
        started: false,
        completedTrainings: 0,
        totalTrainings: challengeConfig[challenge.challengeName].totalTrainings
      });
    }
  });

  console.log(`Reset all challenges for health goal ${healthGoalId}`);
};

/**
 * Get training completion dialog data
 * @param {string} challengeName - Name of the challenge
 * @returns {Object} Dialog configuration data
 */
export const getTrainingCompletionDialogData = (challengeName) => {
  const status = getChallengeStatus(challengeName);
  if (!status.found) {
    return null;
  }

  const canRecordTraining = status.started && !status.challengeCompleted;
  const remainingTrainings = Math.max(0, status.totalTrainings - status.completedTrainings);

  return {
    challengeName: status.challengeName,
    displayName: status.displayName,
    canRecordTraining,
    completedTrainings: status.completedTrainings,
    totalTrainings: status.totalTrainings,
    remainingTrainings,
    completionPercentage: status.completionPercentage,
    challengeCompleted: status.challengeCompleted
  };
};
