/**
 * Health Goal Utilities
 * 
 * This module provides utility functions for working with health goals,
 * integrating the configuration system with the appStorage and providing
 * convenient methods for health goal management.
 */

import { healthGoalsConfig, getHealthGoalConfig } from '../config/healthGoalsConfig.js';
import { appStorage } from '../../utils.js';

/**
 * Initialize health goal system
 * This should be called during app initialization to ensure
 * appStorage is synchronized with the configuration
 */
export const initializeHealthGoalSystem = () => {
  // Initialize health goal progress from configuration
  appStorage.initializeHealthGoalProgressFromConfig(healthGoalsConfig);
  
  console.log('Health Goal System initialized');
};

/**
 * Get health goal configuration with dynamic progress data
 * @param {string} goalId - The health goal ID
 * @returns {Object|null} Combined configuration and progress data
 */
export const getHealthGoalWithProgress = (goalId) => {
  console.log(`🔍 getHealthGoalWithProgress() called for goalId: ${goalId}`);

  const config = getHealthGoalConfig(goalId);
  console.log('📋 Health goal config:', config);
  if (!config) {
    console.log('❌ No config found for goalId:', goalId);
    return null;
  }

  const progress = appStorage.getHealthGoalProgress(goalId);
  console.log('📈 Health goal progress:', progress);

  const isActive = appStorage.activeHealthGoals[goalId] || false;
  console.log(`🎯 Health goal ${goalId} active status:`, isActive);
  console.log('📊 All activeHealthGoals:', appStorage.activeHealthGoals);

  const result = {
    ...config,
    ...progress,
    active: isActive,
    // Use active image if available and goal is active
    cardImage: isActive && config.cardImageActive ? config.cardImageActive : config.cardImage
  };

  console.log('✅ Final health goal with progress result:', result);
  return result;
};

/**
 * Get all health goals with their current progress
 * @returns {Array} Array of health goals with progress data
 */
export const getAllHealthGoalsWithProgress = () => {
  return Object.keys(healthGoalsConfig).map(goalId => getHealthGoalWithProgress(goalId));
};

/**
 * Get health goal card properties for rendering
 * @param {string} goalId - The health goal ID
 * @param {boolean} isActiveCard - Whether to render as active card
 * @returns {Object|null} Properties object for health goal card component
 */
export const getHealthGoalCardProps = (goalId, isActiveCard = false) => {
  console.log(`🎨 getHealthGoalCardProps() called for goalId: ${goalId}, isActiveCard: ${isActiveCard}`);

  const healthGoal = getHealthGoalWithProgress(goalId);
  if (!healthGoal) {
    console.log('❌ No health goal data found for card props');
    return null;
  }
  
  // Determine which image to use
  let cardImage = healthGoal.cardImage;
  if (isActiveCard && healthGoal.cardImageActive) {
    cardImage = healthGoal.cardImageActive;
  } else if (!isActiveCard && healthGoal.cardImageInactive) {
    cardImage = healthGoal.cardImageInactive;
  }
  
  const cardProps = {
    cardImage,
    tagCategory: healthGoal.tagCategory,
    tagIcon: healthGoal.tagIcon,
    tagColor: healthGoal.tagColor,
    tagTextColor: healthGoal.tagTextColor,
    healthgoalTitle: healthGoal.healthgoalTitle,
    healthgoalCoop: healthGoal.healthgoalCoop,
    pillText: isActiveCard ? healthGoal.pillText : "",
    pillColor: healthGoal.pillColor,
    goalinfoIcon1: healthGoal.goalinfoIcon1,
    goalinfoText1: healthGoal.goalinfoText1,
    goalinfoIcon2: healthGoal.goalinfoIcon2,
    goalinfoText2: healthGoal.goalinfoText2,
    completedChallenges: healthGoal.completedChallenges,
    totalChallenges: healthGoal.totalChallenges,
    link: healthGoal.link
  };

  console.log('🎨 Generated card props:', cardProps);
  console.log(`📊 Progress display: ${cardProps.completedChallenges} von ${cardProps.totalChallenges} Challenges geschafft`);

  return cardProps;
};

/**
 * Get the currently active health goal
 * @returns {Object|null} Active health goal with progress data or null
 */
export const getActiveHealthGoal = () => {
  console.log('🔍 getActiveHealthGoal() called');
  console.log('📊 appStorage.activeHealthGoals:', appStorage.activeHealthGoals);
  console.log('📊 appStorage._data.activeHealthGoals:', appStorage._data.activeHealthGoals);

  const activeGoals = Object.keys(appStorage.activeHealthGoals)
    .filter(goalId => appStorage.activeHealthGoals[goalId]);

  console.log('🎯 Filtered active goals:', activeGoals);

  if (activeGoals.length === 0) {
    console.log('❌ No active health goals found');
    return null;
  }

  console.log(`✅ Found active health goal: ${activeGoals[0]}`);

  // Return the first active goal (system supports only one active goal)
  const result = getHealthGoalWithProgress(activeGoals[0]);
  console.log('📈 Health goal with progress:', result);

  return result;
};

/**
 * Activate a health goal
 * @param {string} goalId - The health goal ID to activate
 * @returns {boolean} True if activation was successful
 */
export const activateHealthGoal = (goalId) => {
  const config = getHealthGoalConfig(goalId);
  if (!config) {
    console.error(`Health goal ${goalId} not found in configuration`);
    return false;
  }
  
  // Deactivate all other health goals (only one can be active)
  Object.keys(appStorage.activeHealthGoals).forEach(id => {
    appStorage.activeHealthGoals[id] = false;
  });
  
  // Activate the specified health goal
  appStorage.activateHealthGoal(goalId);
  
  console.log(`Health goal ${goalId} activated`);
  return true;
};

/**
 * Get health goal categories for filtering
 * @returns {Array} Array of category objects with name and count
 */
export const getHealthGoalCategoriesWithCount = () => {
  const allGoals = getAllHealthGoalsWithProgress();
  const categoryCount = {};
  
  allGoals.forEach(goal => {
    const category = goal.tagCategory;
    categoryCount[category] = (categoryCount[category] || 0) + 1;
  });
  
  return Object.keys(categoryCount).map(category => ({
    name: category,
    count: categoryCount[category]
  }));
};

/**
 * Update health goal progress and check for completion
 * @param {string} goalId - The health goal ID
 * @param {string} challengeName - The challenge name
 * @param {number} completedTrainings - Number of completed trainings
 * @param {number} totalTrainings - Total number of trainings required
 */
export const updateHealthGoalProgress = (goalId, challengeName, completedTrainings, totalTrainings) => {
  appStorage.updateChallengeTrainingProgress(goalId, challengeName, completedTrainings, totalTrainings);
  
  // Log progress update
  const progress = appStorage.getHealthGoalProgress(goalId);
  console.log(`Health goal ${goalId} progress updated:`, {
    completedChallenges: progress.completedChallenges,
    totalChallenges: progress.totalChallenges,
    challengeName,
    trainingProgress: `${completedTrainings}/${totalTrainings}`
  });
};

/**
 * Reset all health goal progress
 * @param {boolean} verbose - Whether to log detailed reset information
 */
export const resetHealthGoalProgress = (verbose = true) => {
  try {
    if (verbose) {
      console.log('🔄 Starting complete health goal system reset...');
    }

    // Get current state for logging
    const currentState = verbose ? {
      activeGoals: Object.keys(appStorage.activeHealthGoals).filter(id => appStorage.activeHealthGoals[id]),
      totalProgress: Object.keys(appStorage._data.healthGoalProgress || {}).map(goalId => ({
        goalId,
        progress: appStorage.getHealthGoalProgress(goalId)
      }))
    } : null;

    if (verbose && currentState.activeGoals.length > 0) {
      console.log('📊 Current active goals before reset:', currentState.activeGoals);
      console.log('📈 Current progress before reset:', currentState.totalProgress);
    }

    // Reset appStorage which will reinitialize from config
    appStorage.reset();

    // Reinitialize the health goal system with fresh data
    initializeHealthGoalSystem();

    if (verbose) {
      console.log('✅ Health goal system reset complete');
      console.log('🔧 System reinitialized with default configuration');

      // Log new state
      const newState = {
        healthGoalProgress: appStorage._data.healthGoalProgress,
        activeGoals: Object.keys(appStorage.activeHealthGoals).filter(id => appStorage.activeHealthGoals[id])
      };
      console.log('📊 New state after reset:', newState);
    }

    return { success: true, message: 'Health goal progress reset successfully' };
  } catch (error) {
    console.error('❌ Error during health goal reset:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Reset a specific health goal's progress
 * @param {string} goalId - The health goal ID to reset
 * @param {boolean} verbose - Whether to log detailed reset information
 */
export const resetSpecificHealthGoal = (goalId, verbose = true) => {
  try {
    if (verbose) {
      console.log(`🔄 Resetting health goal: ${goalId}`);
    }

    // Deactivate the health goal
    if (appStorage.activeHealthGoals[goalId]) {
      appStorage.activeHealthGoals[goalId] = false;
    }

    // Reset progress data
    if (appStorage._data.healthGoalProgress && appStorage._data.healthGoalProgress[goalId]) {
      const config = getHealthGoalConfig(goalId);
      appStorage._data.healthGoalProgress[goalId] = {
        completedChallenges: 0,
        totalChallenges: config?.totalChallenges || 0,
        challengeCompletions: {}
      };
    }

    // Reset related challenges in old system
    const challengesToReset = Object.keys(appStorage._data.challenges || {});
    challengesToReset.forEach(challengeName => {
      if (appStorage._data.challenges[challengeName]) {
        appStorage.updateChallenge(challengeName, {
          started: false,
          completedTrainings: 0,
          totalTrainings: appStorage._data.challenges[challengeName].totalTrainings || 0
        });
      }
    });

    appStorage._save();

    if (verbose) {
      console.log(`✅ Health goal ${goalId} reset successfully`);
    }

    return { success: true, goalId, message: `Health goal ${goalId} reset successfully` };
  } catch (error) {
    console.error(`❌ Error resetting health goal ${goalId}:`, error);
    return { success: false, goalId, error: error.message };
  }
};

/**
 * Validate that the health goal system is properly initialized
 * @returns {Object} Validation result
 */
export const validateHealthGoalSystem = () => {
  try {
    const issues = [];
    const warnings = [];

    // Check if appStorage is initialized
    if (!appStorage._data) {
      issues.push('appStorage._data is not initialized');
    }

    // Check if health goal progress structure exists
    if (!appStorage._data.healthGoalProgress) {
      issues.push('healthGoalProgress structure is missing');
    }

    // Check if fitUmgebung health goal is configured
    if (!appStorage._data.healthGoalProgress?.fitUmgebung) {
      issues.push('fitUmgebung health goal is not configured');
    }

    // Check if configuration is loaded
    const config = getHealthGoalConfig('fitUmgebung');
    if (!config) {
      issues.push('Health goal configuration is not loaded');
    }

    // Check if totalChallenges matches configuration
    const progress = appStorage.getHealthGoalProgress('fitUmgebung');
    if (config && progress.totalChallenges !== config.totalChallenges) {
      warnings.push(`totalChallenges mismatch: storage=${progress.totalChallenges}, config=${config.totalChallenges}`);
    }

    const isValid = issues.length === 0;

    return {
      valid: isValid,
      issues,
      warnings,
      summary: isValid ? 'Health goal system is properly initialized' : 'Health goal system has issues'
    };
  } catch (error) {
    return {
      valid: false,
      issues: [`Validation error: ${error.message}`],
      warnings: [],
      summary: 'Failed to validate health goal system'
    };
  }
};

/**
 * Verify complete data flow from appStorage to UI rendering
 * This function traces the entire data flow for debugging purposes
 * @param {string} goalId - The health goal ID to trace (default: 'fitUmgebung')
 * @returns {Object} Complete data flow trace
 */
export const verifyDataFlowConnections = (goalId = 'fitUmgebung') => {
  console.log('🔍 === DATA FLOW VERIFICATION START ===');
  console.log(`🎯 Tracing data flow for health goal: ${goalId}`);

  const trace = {
    goalId,
    timestamp: new Date().toISOString(),
    steps: {}
  };

  // Step 1: Check appStorage raw data
  console.log('📊 Step 1: Checking appStorage raw data');
  trace.steps.appStorageRaw = {
    activeHealthGoals: appStorage._data.activeHealthGoals,
    healthGoalProgress: appStorage._data.healthGoalProgress?.[goalId],
    activeHealthGoalsGetter: appStorage.activeHealthGoals
  };
  console.log('Raw appStorage data:', trace.steps.appStorageRaw);

  // Step 2: Test getActiveHealthGoal()
  console.log('📊 Step 2: Testing getActiveHealthGoal()');
  const activeHealthGoal = getActiveHealthGoal();
  trace.steps.activeHealthGoal = activeHealthGoal;
  console.log('Active health goal result:', activeHealthGoal);

  // Step 3: Test getHealthGoalWithProgress()
  console.log('📊 Step 3: Testing getHealthGoalWithProgress()');
  const healthGoalWithProgress = getHealthGoalWithProgress(goalId);
  trace.steps.healthGoalWithProgress = healthGoalWithProgress;
  console.log('Health goal with progress:', healthGoalWithProgress);

  // Step 4: Test getHealthGoalCardProps()
  console.log('📊 Step 4: Testing getHealthGoalCardProps()');
  const cardProps = getHealthGoalCardProps(goalId, true);
  trace.steps.cardProps = cardProps;
  console.log('Card props for active card:', cardProps);

  // Step 5: Check configuration
  console.log('📊 Step 5: Checking configuration');
  const config = getHealthGoalConfig(goalId);
  trace.steps.config = config;
  console.log('Health goal configuration:', config);

  // Step 6: Validate data consistency
  console.log('📊 Step 6: Validating data consistency');
  const validation = {
    hasActiveGoal: !!activeHealthGoal,
    activeGoalMatchesTarget: activeHealthGoal?.id === goalId,
    hasProgress: !!healthGoalWithProgress,
    hasCardProps: !!cardProps,
    hasConfig: !!config,
    progressDataConsistent: healthGoalWithProgress?.completedChallenges !== undefined && healthGoalWithProgress?.totalChallenges !== undefined
  };
  trace.steps.validation = validation;
  console.log('Data consistency validation:', validation);

  // Summary
  const isDataFlowWorking = validation.hasActiveGoal && validation.activeGoalMatchesTarget &&
                           validation.hasProgress && validation.hasCardProps && validation.hasConfig;

  trace.summary = {
    isDataFlowWorking,
    issues: Object.keys(validation).filter(key => !validation[key]),
    progressDisplay: cardProps ? `${cardProps.completedChallenges} von ${cardProps.totalChallenges} Challenges geschafft` : 'No progress data'
  };

  console.log('🔍 === DATA FLOW VERIFICATION SUMMARY ===');
  console.log(`✅ Data flow working: ${isDataFlowWorking}`);
  console.log(`📊 Progress display: ${trace.summary.progressDisplay}`);
  if (trace.summary.issues.length > 0) {
    console.log(`❌ Issues found: ${trace.summary.issues.join(', ')}`);
  }
  console.log('🔍 === DATA FLOW VERIFICATION END ===');

  return trace;
};

/**
 * Get health goal completion percentage
 * @param {string} goalId - The health goal ID
 * @returns {number} Completion percentage (0-100)
 */
export const getHealthGoalCompletionPercentage = (goalId) => {
  const progress = appStorage.getHealthGoalProgress(goalId);
  if (progress.totalChallenges === 0) return 0;

  return Math.round((progress.completedChallenges / progress.totalChallenges) * 100);
};
