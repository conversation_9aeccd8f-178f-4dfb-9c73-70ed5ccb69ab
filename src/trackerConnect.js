import { html } from "lit-html";
import { topMenu } from "../webcomponents/topMenu.js";
import { titleDuoColor } from "../webcomponents/titleDuoColor.js";
import { sectionParagraph } from "../webcomponents/sectionParagraph.js";
import { buttonStandard } from "../webcomponents/buttonStandard.js";
import { buttonTextIcon } from "../webcomponents/buttonTextIcon.js";
import { listItem } from "../webcomponents/listItem.js";
import { exitWithSlide, router } from "../router.js";
import iconArrowLeft from "../svg/icons/ArrowLeft.svg";
import iconDotMenu from "../svg/icons/PointsVertical.svg";
import imgKomoot from "../img/logos/logo_komoot.png";
import imgFitbit from "../img/logos/logo_fitbit.png";
import imgGarmin from "../img/logos/logo_garmin.png";
import imgHealth from "../img/logos/logo_applehealth.png";
import imgOura from "../img/logos/logo_oura.png";
import imgPolar from "../img/logos/logo_polar.png";
import imgSuunto from "../img/logos/logo_suunto.png";
import imgStrava from "../img/logos/logo_strava.png";
import imgWithings from "../img/logos/logo_withings.png";
import { appStorage } from "../utils.js";
import { startChallenge } from "./utils/challengeCompletionUtils.js";
import "../webcomponents/disconnectButton.css";

  /**
   * Scrollt animiert zum Speichern-Button
   */
  const scrollToSaveButton = () => {
    setTimeout(() => {
      const saveButton = document.querySelector('.content-bottom-padding button');
      if (saveButton) {
        saveButton.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    }, 300); // Kurze Verzögerung für bessere UX
  };

/**
 * Top Menu Template mit Slide-Out-Animation beim Zurück-Button
 * @returns {TemplateResult} Das Top-Menu Template
 */
const templateTopMenu = () => {
  // Bestimme das Zurück-Ziel basierend auf dem Status der Challenge
  const handleBack = () => {
    // Wenn komoot verbunden ist und wir von der Challenge-Seite kommen, löse das gleiche Ereignis aus wie der Speichern-Button
    if (appStorage.connectedApps && appStorage.connectedApps.komoot) {
      saveAndNavigateBack();
    } else {
      // Sonst zurück zur Challenge-Seite
      exitWithSlide("/challenge/lockere-wanderung");
    }
  };

  return html`
    ${topMenu({
      backIcon: iconArrowLeft,
      primaryIcon: "",
      menuIcon: iconDotMenu,
      onBack: handleBack
    })}
  `;
};

/**
 * Erstellt einen Button zum Verbinden oder Trennen
 * @param {Function} onClick - Click-Handler für den Button
 * @param {boolean} isConnected - Gibt an, ob bereits verbunden ist
 * @returns {TemplateResult} Der Button ohne Icon
 */
const connectButton = (onClick, isConnected = false) => {
  // Wir verwenden buttonTextIcon ohne ein Icon zu übergeben
  const buttonText = isConnected ? "Trennen" : "Verbinden";
  const buttonClass = isConnected ? "disconnect-button" : "";

  const button = html`
    <div @click=${onClick} style="cursor: pointer;" class="${buttonClass}">
      ${buttonTextIcon(buttonText)}
    </div>
  `;
  return button;
};

/**
 * Haupttemplate für die Fitness-App Verbindungsseite
 * @returns {TemplateResult} Das Haupttemplate
 */
export const trackerConnectTemplate = () => {
  // Prüfen, ob Komoot bereits verbunden ist
  if (!appStorage.connectedApps) {
    appStorage._data.connectedApps = {};
  }

  // Prüfen, ob mindestens eine App verbunden ist
  const isAnyAppConnected = appStorage.connectedApps && 
    Object.values(appStorage.connectedApps).some(value => value === true);

  // Prüfen, ob wir von der Komoot-Login-Seite kommen
  const comingFromKomootLogin = appStorage.connectedApps && appStorage.connectedApps.komoot;
  
  // Wenn wir von der Komoot-Login-Seite kommen und eine App verbunden ist, scrolle zum Button
  if (comingFromKomootLogin && isAnyAppConnected) {
    scrollToSaveButton();
  }

  // Funktion zum Verbinden/Trennen von Komoot
  const handleKomootConnection = () => {
    if (appStorage.connectedApps.komoot) {
      // Trennen
      appStorage.setAppConnection('komoot', false);
      console.log("Komoot getrennt");
      // Seite neu rendern
      router.navigate("/tracker-connect");
    } else {
      // Zur Login-Seite navigieren
      router.navigate("/komoot-login");
    }
  };

  // Erstelle die Listeneinträge für die Fitness-Apps
  const komootItem = {
    text: "komoot",
    icon: imgKomoot,
    iconPosition: "left",
    customContent: connectButton(handleKomootConnection, appStorage.connectedApps && appStorage.connectedApps.komoot)
  };

  const fitbitItem = {
    text: "Fitbit",
    icon: imgFitbit,
    iconPosition: "left",
    customContent: connectButton(() => console.log("Fitbit verbinden geklickt"))
  };

  const garminItem = {
    text: "Garmin",
    icon: imgGarmin,
    iconPosition: "left",
    customContent: connectButton(() => console.log("Garmin verbinden geklickt"))
  };

  const healthItem = {
    text: "Health",
    icon: imgHealth,
    iconPosition: "left",
    customContent: connectButton(() => console.log("Health verbinden geklickt"))
  };

  const ouraItem = {
    text: "Oura",
    icon: imgOura,
    iconPosition: "left",
    customContent: connectButton(() => console.log("Oura verbinden geklickt"))
  };

  const polarItem = {
    text: "Polar",
    icon: imgPolar,
    iconPosition: "left",
    customContent: connectButton(() => console.log("Polar verbinden geklickt"))
  };

  const suuntoItem = {
    text: "Suunto",
    icon: imgSuunto,
    iconPosition: "left",
    customContent: connectButton(() => console.log("Suunto verbinden geklickt"))
  };

  const stravaItem = {
    text: "Strava",
    icon: imgStrava,
    iconPosition: "left",
    customContent: connectButton(() => console.log("Strava verbinden geklickt"))
  };

  const withingsItem = {
    text: "Withings",
    icon: imgWithings,
    iconPosition: "left",
    customContent: connectButton(() => console.log("Withings verbinden geklickt"))
  };

  // Liste aller Fitness-Apps
  const trackerItems = [
    komootItem,
    fitbitItem,
    garminItem,
    healthItem,
    ouraItem,
    polarItem,
    suuntoItem,
    stravaItem,
    withingsItem
  ];

  // Funktion zum Speichern und Zurücknavigieren zur Challenge-Seite
  const saveAndNavigateBack = () => {
    console.log("Einstellungen gespeichert");

    // Use new challenge completion system
    const result = startChallenge('lockereWanderung');
    if (result.success) {
      console.log(`Challenge ${result.displayName} gestartet`);
    } else {
      console.error("Fehler beim Starten der Challenge:", result.error);
      // Fallback to old system
      appStorage.updateChallenge('lockereWanderung', {
        started: true,
        completedTrainings: 0,
        totalTrainings: 2
      });
    }

    // Navigiere zurück zur Challenge-Seite
    router.navigate("/challenge/lockere-wanderung");
  };

  return html`
    ${templateTopMenu()}
    <div class="content-left-align content-padding content-top-padding">
      ${titleDuoColor("Fitness-App", "verbinden", "h1")}

      ${sectionParagraph(
        "Mit der Auswahl einer Fitness-App autorisierst Du die AOK NAVIDA-App, Deine Schritte, Kalorien oder Herzfrequenz zu prüfen.", false, true
      )}

      <div class="fitness-apps-list black-text">
        ${listItem(trackerItems)}
      </div>
      
      <!-- Speichern Button - deaktiviert wenn keine App verbunden ist -->
      <div class="content-bottom-padding">
        ${buttonStandard({
          text: "Speichern",
          variant: "primary",
          disabled: !isAnyAppConnected,
          onClick: saveAndNavigateBack
        })}
      </div>
    </div>
  `;
};

console.log("trackerConnect.js loaded");
