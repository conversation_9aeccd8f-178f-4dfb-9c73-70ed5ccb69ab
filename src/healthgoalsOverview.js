import { html } from "lit-html";
import { BottomNavigation } from "./bottomNavigation.js";
import { topMenu } from "../webcomponents/topMenu.js";
import iconArrowLeft from "../svg/icons/ArrowLeft.svg";
import iconDotMenu from "../svg/icons/PointsVertical.svg";
import iconSmartwatch from "../svg/icons/icon_smartwatch.svg";
import { sectionTitle } from "../webcomponents/sectionTitle.js";
import { sectionSubtitle } from "../webcomponents/sectionSubtitle.js";
import { titleDuoColor } from "../webcomponents/titleDuoColor.js";
import { createSegmentedControl } from "../webcomponents/segmentedControl.js";
import { healthgoalOverview_segment1Content } from "../webcomponents/segments/healthgoalOverview_Segment1.js";
import { healthgoalOverview_segment2Content } from "../webcomponents/segments/healthgoalOverview_Segment2.js";
import { exitWithSlide } from "../router.js"; // Absoluter Pfad vom Projektroot
import { infoCardSlider } from "../webcomponents/infoCardSlider.js";
import iconBell from "../svg/icons/icon_bell.svg";
import { createChips } from "../webcomponents/chip_filterChip.js";
import { healthgoalCardInactive } from "../webcomponents/healthgoalCardInactive.js";
import imgKomootPortrait from "../img/healthgoals/komoot_portrait.png";
import imgPeople from "../img/people_hugging.jpg";
import imgNatur from "../img/healthgoals/hg_aktivInDerNatur.jpg";
import imgErnaehrung from "../img/healthgoals/hg_issDichGesund.jpg";
import imgBewegung from "../img/healthgoals/hg_mehrBewegungImAlltag.jpg";
import imgGelassenheit from "../img/healthgoals/hg_mehrGelassenheit.jpg";
import imgMuskeln from "../img/healthgoals/hg_staerkeDeineMuskeln.jpg";
import imgAusdauer from "../img/healthgoals/hg_steigereDeineAusdauer.jpg";
import iconTagSport from "../svg/icons/icon_tag_sport.svg";
import iconTagSleep from "../svg/icons/icon_tag_sleep.svg";
import iconTagPsych from "../svg/icons/icon_tag_brain.svg";
import iconTagFood from "../svg/icons/icon_tag_food.svg";
import iconMoneypig from "../svg/icons/icon_moneypig.svg";
import iconTimelater from "../svg/icons/icon_timelater.svg";
import { createCardFilter } from "./utils/cardFilter.js";
import { appStorage } from "../utils.js";

/* Top Menu mit Slide-Out-Animation beim Zurück-Button */
const templateTopMenu = () => html`
  ${topMenu({
    backIcon: iconArrowLeft,
    primaryIcon: iconSmartwatch,
    menuIcon: iconDotMenu,
    onBack: () => exitWithSlide("/features") // Verwende exitWithSlide statt history.back()
  })}
`;

/* Segmented Control */
const segments = [
  {
    id: "Segment1",
    title: "Ziele",
    content: healthgoalOverview_segment1Content(), // Call the function to get fresh template
  },
  {
    id: "Segment2",
    title: "Bonuspunkte",
    content: healthgoalOverview_segment2Content,
  },
];
export const segmentedControl = createSegmentedControl(segments);

/* Section (Sub) Titles */
export const templateSectionTitle1 = () => html`
  ${sectionTitle("Was möchtest Du Dir vornehmen?")}
`;

// Content für die "no-goal" Variante
const noGoalContent = () => {
  // Erstelle Filter-Funktion
  const filterCards = createCardFilter(
    '.healthgoal-cards-container', 
    '.healthgoal-card'
  );
  
  // Filter Chips erstellen mit Callback für Filteränderungen
  const filterChips = createChips("Bewegung Schlaf Psyche Ernährung", filterCards);
  
  // Nach dem Rendern initialisieren
  setTimeout(() => {
    if (filterChips && typeof filterChips.initialize === 'function') {
      filterChips.initialize();
      
      // Manuell die data-category Attribute für alle Karten setzen
      const container = document.querySelector('.healthgoal-cards-container');
      if (container) {
        // Alle Karten mit ihren Kategorien
        const cardCategories = [
          "Bewegung", // Fit in deiner Umgebung
          "Schlaf",   // Gesunder Schlaf
          "Bewegung", // Aktiv in der Natur
          "Ernährung", // Iss Dich gesund
          "Bewegung", // Mehr Bewegung im Alltag
          "Psyche",   // Finde zu mehr Gelassenheit
          "Bewegung", // Stärke Deine Muskeln
          "Bewegung"  // Steigere Deine Ausdauer
        ];
        
        const cards = container.querySelectorAll('.healthgoal-card');
        cards.forEach((card, index) => {
          if (index < cardCategories.length) {
            card.setAttribute('data-category', cardCategories[index]);
          }
        });
      }
    }
  }, 0);
  
  return html`
  <div class="content-padding">
    ${infoCardSlider({
      icoPath: iconBell,
      title: "Du möchtest nichts vergessen?",
      text: "Stelle hier meine Erinnerungen an Dich ein",
      linkText: "Einstellungen",
    })}
  </div>
  <div class="content-padding standard-container">
    ${sectionSubtitle("Alle Gesundheitsziele")}
    <div class="standard-container">
      ${filterChips.template}
    </div>
    <div class="healthgoal-cards-container">
      ${healthgoalCardInactive({
        cardImage: imgKomootPortrait,
        tagCategory: "Bewegung",
        tagIcon: iconTagSport,
        tagColor: "--tag-sport",
        tagTextColor: "--tag-text",
        healthgoalTitle: "Fit in deiner Umgebung",
        healthgoalCoop: "In Kooperation mit Komoot",
        pillText: "",
        pillColor: "--primary-brand",
        goalinfoIcon1: iconMoneypig,
        goalinfoText1: "1500 Bonuspunkte",
        goalinfoIcon2: iconTimelater,
        goalinfoText2: "max. 90 Tage",
        link: "/healthgoals-overview/hg-fitUmgebung"
      })}
      ${healthgoalCardInactive({
        cardImage: imgPeople,
        tagCategory: "Schlaf",
        tagIcon: iconTagSleep,
        tagColor: "--tag-sleep",
        tagTextColor: "--tag-text",
        healthgoalTitle: "Gesunder Schlaf",
        healthgoalCoop: "",
        pillText: "",
        pillColor: "--primary-brand",
        goalinfoIcon1: iconMoneypig,
        goalinfoText1: "1500 Bonuspunkte",
        goalinfoIcon2: iconTimelater,
        goalinfoText2: "max. 90 Tage",
      })}
      ${healthgoalCardInactive({
        cardImage: imgNatur,
        tagCategory: "Bewegung",
        tagIcon: iconTagSport,
        tagColor: "--tag-sport",
        tagTextColor: "--tag-text",
        healthgoalTitle: "Aktiv in der Natur",
        healthgoalCoop: "",
        pillText: "",
        pillColor: "--primary-brand",
        goalinfoIcon1: iconMoneypig,
        goalinfoText1: "1500 Bonuspunkte",
        goalinfoIcon2: iconTimelater,
        goalinfoText2: "max. 90 Tage",
        dataCategory: "Bewegung" // Kategorie für Filter explizit setzen
      })}
      ${healthgoalCardInactive({
        cardImage: imgErnaehrung,
        tagCategory: "Ernährung",
        tagIcon: iconTagFood,
        tagColor: "--tag-food",
        tagTextColor: "--tag-text",
        healthgoalTitle: "Iss Dich gesund",
        healthgoalCoop: "",
        pillText: "",
        pillColor: "--primary-brand",
        goalinfoIcon1: iconMoneypig,
        goalinfoText1: "1500 Bonuspunkte",
        goalinfoIcon2: iconTimelater,
        goalinfoText2: "max. 90 Tage",
      })}
      ${healthgoalCardInactive({
        cardImage: imgBewegung,
        tagCategory: "Bewegung",
        tagIcon: iconTagSport,
        tagColor: "--tag-sport",
        tagTextColor: "--tag-text",
        healthgoalTitle: "Mehr Bewegung im Alltag",
        healthgoalCoop: "",
        pillText: "",
        pillColor: "--primary-brand",
        goalinfoIcon1: iconMoneypig,
        goalinfoText1: "1500 Bonuspunkte",
        goalinfoIcon2: iconTimelater,
        goalinfoText2: "max. 90 Tage",
      })}
      ${healthgoalCardInactive({
        cardImage: imgGelassenheit,
        tagCategory: "Psyche",
        tagIcon: iconTagPsych,
        tagColor: "--tag-psych",
        tagTextColor: "--tag-text",
        healthgoalTitle: "Finde zu mehr Gelassenheit",
        healthgoalCoop: "",
        pillText: "",
        pillColor: "--primary-brand",
        goalinfoIcon1: iconMoneypig,
        goalinfoText1: "1500 Bonuspunkte",
        goalinfoIcon2: iconTimelater,
        goalinfoText2: "max. 90 Tage",
      })}
      ${healthgoalCardInactive({
        cardImage: imgMuskeln,
        tagCategory: "Bewegung",
        tagIcon: iconTagSport,
        tagColor: "--tag-sport",
        tagTextColor: "--tag-text",
        healthgoalTitle: "Stärke Deine Muskeln",
        healthgoalCoop: "",
        pillText: "",
        pillColor: "--primary-brand",
        goalinfoIcon1: iconMoneypig,
        goalinfoText1: "1500 Bonuspunkte",
        goalinfoIcon2: iconTimelater,
        goalinfoText2: "max. 90 Tage",
      })}
      ${healthgoalCardInactive({
        cardImage: imgAusdauer,
        tagCategory: "Bewegung",
        tagIcon: iconTagSport,
        tagColor: "--tag-sport",
        tagTextColor: "--tag-text",
        healthgoalTitle: "Steigere Deine Ausdauer",
        healthgoalCoop: "",
        pillText: "",
        pillColor: "--primary-brand",
        goalinfoIcon1: iconMoneypig,
        goalinfoText1: "1500 Bonuspunkte",
        goalinfoIcon2: iconTimelater,
        goalinfoText2: "max. 90 Tage",
      })}
    </div>
  </div>
  `;
};

// Haupttemplate mit Parameter für aktives Ziel
export const healthgoalsOverviewTemplate = (activeGoal = "fitInDeinerUmgebung") => {
  // Inhalt basierend auf dem aktiven Ziel
  const segmentedControlContent = activeGoal === "noGoal" 
    ? noGoalContent() 
    : segmentedControl.template;

  // Bottom-Navigation nur anzeigen, wenn nicht "noGoal"
  const showBottomNav = activeGoal !== "noGoal";

  return html`
    ${templateTopMenu()}
    <div class="content-left-align content-padding content-no-bottom-margin">
      ${titleDuoColor("Deine", "Ziele")}
    </div>
    ${activeGoal === "noGoal" 
      ? html`
        ${templateSectionTitle1()}
        <div class="content-left-align content-padding black-text">
          <p>
            Stecke Dir Ziele in unterschiedlichen Bereichen oder mit gemischten Zielen.
          </p>
        </div>
      ` 
      : ``
    }
    <div id="segmentedControlContainer" class="standard-container">
      ${segmentedControlContent}
      <div class="healthgoal-cards content-padding"></div>
    </div>
    <!-- Empty Container für Abstand nach unten -->
    <!-- <div
      class="standard-container content-padding black-text empty-container"
    ></div>
  -->
    ${showBottomNav ? BottomNavigation("home") : ''}
  `;
};

// Spezifische Templates für verschiedene Zustände
export const healthgoalsOverviewWithActiveGoalTemplate = () => {
  // Prüfen, ob das fitUmgebung-Ziel im appStorage aktiviert ist
  const hasActiveGoal = appStorage && 
                        appStorage._data && 
                        appStorage._data.activeHealthGoals && 
                        appStorage._data.activeHealthGoals.fitUmgebung;
  
  console.log("Active health goal status:", hasActiveGoal, appStorage._data.activeHealthGoals);
  
  // Entsprechendes Template basierend auf dem Status zurückgeben
  return healthgoalsOverviewTemplate(hasActiveGoal ? "fitInDeinerUmgebung" : "noGoal");
};

export const healthgoalsOverviewNoGoalTemplate = () => healthgoalsOverviewTemplate("noGoal");

console.log("healthgoalsoverview.js loaded");
