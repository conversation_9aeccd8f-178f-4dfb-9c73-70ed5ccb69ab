/**
 * Centralized Health Goals Configuration
 * 
 * This file contains all health goal data including images, categories, colors,
 * titles, cooperation info, goal info texts, and challenge configuration.
 * 
 * Data Structure:
 * - Each health goal has a unique key (e.g., 'fitUmgebung')
 * - Contains all visual and textual properties
 * - Includes totalChallenges for progress tracking
 * - Supports both active and inactive card configurations
 */

// Import all required images
import imgKomootHG from "../../img/healthgoals/komoot_hg.jpg";
import imgKomootPortrait from "../../img/healthgoals/komoot_portrait.png";
import imgPeople from "../../img/people_hugging.jpg";
import imgNatur from "../../img/healthgoals/hg_aktivInDerNatur.jpg";
import imgErnaehrung from "../../img/healthgoals/hg_issDichGesund.jpg";
import imgBewegung from "../../img/healthgoals/hg_mehrBewegungImAlltag.jpg";
import imgGelassenheit from "../../img/healthgoals/hg_mehrGelassenheit.jpg";
import imgMuskeln from "../../img/healthgoals/hg_staerkeDeineMuskeln.jpg";
import imgAusdauer from "../../img/healthgoals/hg_steigereDeineAusdauer.jpg";
import imgCyberfitness from "../../img/cyberfitness.jpg";

// Import all required icons
import iconTagSport from "../../svg/icons/icon_tag_sport.svg";
import iconTagSleep from "../../svg/icons/icon_tag_sleep.svg";
import iconTagPsych from "../../svg/icons/icon_tag_brain.svg";
import iconTagFood from "../../svg/icons/icon_tag_food.svg";
import iconMoneypig from "../../svg/icons/icon_moneypig.svg";
import iconTimelater from "../../svg/icons/icon_timelater.svg";

/**
 * Health Goals Configuration Object
 * 
 * Each health goal contains:
 * - id: Unique identifier
 * - cardImage: Image for the health goal card
 * - cardImageActive: Alternative image for active state (optional)
 * - tagCategory: Category name for filtering
 * - tagIcon: Icon for the category tag
 * - tagColor: CSS variable for tag background color
 * - tagTextColor: CSS variable for tag text color
 * - healthgoalTitle: Display title of the health goal
 * - healthgoalCoop: Cooperation text (optional)
 * - pillText: Text for status pill (optional)
 * - pillColor: CSS variable for pill background color
 * - goalinfoIcon1: First info icon
 * - goalinfoText1: First info text
 * - goalinfoIcon2: Second info icon
 * - goalinfoText2: Second info text
 * - totalChallenges: Total number of challenges for this health goal
 * - link: Navigation link for the health goal (optional)
 * - active: Whether this health goal is currently active
 */
export const healthGoalsConfig = {
  fitUmgebung: {
    id: "fitUmgebung",
    cardImage: imgKomootPortrait,
    cardImageActive: imgKomootHG,
    tagCategory: "Bewegung",
    tagIcon: iconTagSport,
    tagColor: "--tag-sport",
    tagTextColor: "--tag-text",
    healthgoalTitle: "Fit in deiner Umgebung",
    healthgoalCoop: "In Kooperation mit Komoot",
    pillText: "Gerade aktiv",
    pillColor: "--accent-blue",
    goalinfoIcon1: iconMoneypig,
    goalinfoText1: "1500 Bonuspunkte",
    goalinfoIcon2: iconTimelater,
    goalinfoText2: "max. 90 Tage",
    totalChallenges: 4,
    link: "/healthgoals-overview/hg-fitUmgebung",
    active: false
  },

  gesunderSchlaf: {
    id: "gesunderSchlaf",
    cardImage: imgPeople,
    tagCategory: "Schlaf",
    tagIcon: iconTagSleep,
    tagColor: "--tag-sleep",
    tagTextColor: "--tag-text",
    healthgoalTitle: "Gesunder Schlaf",
    healthgoalCoop: "",
    pillText: "",
    pillColor: "--primary-brand",
    goalinfoIcon1: iconMoneypig,
    goalinfoText1: "1500 Bonuspunkte",
    goalinfoIcon2: iconTimelater,
    goalinfoText2: "max. 90 Tage",
    totalChallenges: 3,
    link: "",
    active: false
  },

  aktivInDerNatur: {
    id: "aktivInDerNatur",
    cardImage: imgNatur,
    tagCategory: "Bewegung",
    tagIcon: iconTagSport,
    tagColor: "--tag-sport",
    tagTextColor: "--tag-text",
    healthgoalTitle: "Aktiv in der Natur",
    healthgoalCoop: "",
    pillText: "",
    pillColor: "--primary-brand",
    goalinfoIcon1: iconMoneypig,
    goalinfoText1: "1500 Bonuspunkte",
    goalinfoIcon2: iconTimelater,
    goalinfoText2: "max. 90 Tage",
    totalChallenges: 5,
    link: "",
    active: false
  },

  issDichGesund: {
    id: "issDichGesund",
    cardImage: imgErnaehrung,
    tagCategory: "Ernährung",
    tagIcon: iconTagFood,
    tagColor: "--tag-food",
    tagTextColor: "--tag-text",
    healthgoalTitle: "Iss Dich gesund",
    healthgoalCoop: "",
    pillText: "",
    pillColor: "--primary-brand",
    goalinfoIcon1: iconMoneypig,
    goalinfoText1: "1500 Bonuspunkte",
    goalinfoIcon2: iconTimelater,
    goalinfoText2: "max. 90 Tage",
    totalChallenges: 4,
    link: "",
    active: false
  },

  mehrBewegungImAlltag: {
    id: "mehrBewegungImAlltag",
    cardImage: imgBewegung,
    cardImageInactive: imgCyberfitness,
    tagCategory: "Bewegung",
    tagIcon: iconTagSport,
    tagColor: "--tag-sport",
    tagTextColor: "--tag-text",
    healthgoalTitle: "Mehr Bewegung im Alltag",
    healthgoalCoop: "In Kooperation mit Komoot",
    pillText: "Noch 7 Tage verfügbar",
    pillColor: "--primary-brand",
    goalinfoIcon1: iconMoneypig,
    goalinfoText1: "1500 Bonuspunkte",
    goalinfoIcon2: iconTimelater,
    goalinfoText2: "max. 90 Tage",
    totalChallenges: 6,
    link: "",
    active: false
  },

  mehrGelassenheit: {
    id: "mehrGelassenheit",
    cardImage: imgGelassenheit,
    tagCategory: "Psyche",
    tagIcon: iconTagPsych,
    tagColor: "--tag-psych",
    tagTextColor: "--tag-text",
    healthgoalTitle: "Finde zu mehr Gelassenheit",
    healthgoalCoop: "",
    pillText: "",
    pillColor: "--primary-brand",
    goalinfoIcon1: iconMoneypig,
    goalinfoText1: "1500 Bonuspunkte",
    goalinfoIcon2: iconTimelater,
    goalinfoText2: "max. 90 Tage",
    totalChallenges: 4,
    link: "",
    active: false
  },

  staerkeDeineMuskeln: {
    id: "staerkeDeineMuskeln",
    cardImage: imgMuskeln,
    tagCategory: "Bewegung",
    tagIcon: iconTagSport,
    tagColor: "--tag-sport",
    tagTextColor: "--tag-text",
    healthgoalTitle: "Stärke Deine Muskeln",
    healthgoalCoop: "",
    pillText: "",
    pillColor: "--primary-brand",
    goalinfoIcon1: iconMoneypig,
    goalinfoText1: "1500 Bonuspunkte",
    goalinfoIcon2: iconTimelater,
    goalinfoText2: "max. 90 Tage",
    totalChallenges: 5,
    link: "",
    active: false
  },

  steigereDeineAusdauer: {
    id: "steigereDeineAusdauer",
    cardImage: imgAusdauer,
    tagCategory: "Bewegung",
    tagIcon: iconTagSport,
    tagColor: "--tag-sport",
    tagTextColor: "--tag-text",
    healthgoalTitle: "Steigere Deine Ausdauer",
    healthgoalCoop: "",
    pillText: "",
    pillColor: "--primary-brand",
    goalinfoIcon1: iconMoneypig,
    goalinfoText1: "1500 Bonuspunkte",
    goalinfoIcon2: iconTimelater,
    goalinfoText2: "max. 90 Tage",
    totalChallenges: 4,
    link: "",
    active: false
  }
};

/**
 * Get health goal configuration by ID
 * @param {string} goalId - The health goal ID
 * @returns {Object|null} Health goal configuration or null if not found
 */
export const getHealthGoalConfig = (goalId) => {
  return healthGoalsConfig[goalId] || null;
};

/**
 * Get all health goal configurations as an array
 * @returns {Array} Array of health goal configurations
 */
export const getAllHealthGoals = () => {
  return Object.values(healthGoalsConfig);
};

/**
 * Get health goals filtered by category
 * @param {string} category - The category to filter by
 * @returns {Array} Array of health goal configurations matching the category
 */
export const getHealthGoalsByCategory = (category) => {
  return Object.values(healthGoalsConfig).filter(goal => goal.tagCategory === category);
};

/**
 * Get all unique categories
 * @returns {Array} Array of unique category names
 */
export const getHealthGoalCategories = () => {
  const categories = Object.values(healthGoalsConfig).map(goal => goal.tagCategory);
  return [...new Set(categories)];
};

/**
 * Update health goal configuration
 * @param {string} goalId - The health goal ID
 * @param {Object} updates - Object containing properties to update
 * @returns {boolean} True if update was successful, false otherwise
 */
export const updateHealthGoalConfig = (goalId, updates) => {
  if (healthGoalsConfig[goalId]) {
    healthGoalsConfig[goalId] = { ...healthGoalsConfig[goalId], ...updates };
    return true;
  }
  return false;
};
