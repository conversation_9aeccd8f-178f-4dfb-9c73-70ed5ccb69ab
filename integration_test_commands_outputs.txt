window.finalHealthGoalFix.runWithRetries()
🔄 Attempt 1/3 final_health_goal_fix.js:169:12
🧪 Testing health goal display... final_health_goal_fix.js:130:10
📊 Test results: final_health_goal_fix.js:144:10
  - Segment1 visible: true final_health_goal_fix.js:145:10
  - Has content: true final_health_goal_fix.js:146:10
  - Contains active goal: true final_health_goal_fix.js:147:10
  - Contains active status: true final_health_goal_fix.js:148:10
  - Contains progress: false final_health_goal_fix.js:149:10
⚠️ Some tests failed. Running fix... final_health_goal_fix.js:156:12
🔧 Running final health goal fix... final_health_goal_fix.js:9:10
📊 Step 1: Ensuring health goal is active final_health_goal_fix.js:12:10
✅ Health goal already active final_health_goal_fix.js:17:12
📊 Step 2: Force regenerating template content final_health_goal_fix.js:21:10
✅ Found Segment1 element final_health_goal_fix.js:25:12
📊 Step 3: Force updating segmented control final_health_goal_fix.js:89:10
🔄 Updating segmented control content... final_health_goal_fix.js:92:12
🔄 updateSegmentContent called for: Segment1 segmentedControl.js:10:12
📋 Found segment: 
Object { id: "Segment1", title: "Ziele", content: healthgoalOverview_segment1Content()
 }
segmentedControl.js:12:12
🔄 Updating dynamic content for segment: Segment1 segmentedControl.js:15:14
📍 Content element for Segment1: 
<div id="Segment1" class="tabcontent" style="display: flex;">
segmentedControl.js:17:14
📍 Found content element for Segment1, updating... segmentedControl.js:20:16
🎯 Calling content function for Segment1... segmentedControl.js:24:18
🔄 healthgoalOverview_segment1Content() called - generating fresh template healthgoalOverview_Segment1.js:121:10
🔍 === DATA FLOW VERIFICATION START === healthGoalUtils.js:366:10
🎯 Tracing data flow for health goal: fitUmgebung healthGoalUtils.js:367:10
📊 Step 1: Checking appStorage raw data healthGoalUtils.js:376:10
Raw appStorage data: 
Object { activeHealthGoals: {…}, healthGoalProgress: {…}, activeHealthGoalsGetter: {…} }
healthGoalUtils.js:382:10
📊 Step 2: Testing getActiveHealthGoal() healthGoalUtils.js:385:10
🔍 getActiveHealthGoal() called healthGoalUtils.js:119:10
📊 appStorage.activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:120:10
📊 appStorage._data.activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:121:10
🎯 Filtered active goals: 
Array [ "fitUmgebung" ]
healthGoalUtils.js:126:10
✅ Found active health goal: fitUmgebung healthGoalUtils.js:133:10
🔍 getHealthGoalWithProgress() called for goalId: fitUmgebung healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749811128728", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811128728", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 4, challengeCompletions: {…} }
healthGoalUtils.js:40:10
🎯 Health goal fitUmgebung active status: true healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811128728", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:54:10
📈 Health goal with progress: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811128728", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:137:10
Active health goal result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811128728", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:388:10
📊 Step 3: Testing getHealthGoalWithProgress() healthGoalUtils.js:391:10
🔍 getHealthGoalWithProgress() called for goalId: fitUmgebung healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749811128728", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811128728", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 4, challengeCompletions: {…} }
healthGoalUtils.js:40:10
🎯 Health goal fitUmgebung active status: true healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811128728", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:54:10
Health goal with progress: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811128728", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:394:10
📊 Step 4: Testing getHealthGoalCardProps() healthGoalUtils.js:397:10
🎨 getHealthGoalCardProps() called for goalId: fitUmgebung, isActiveCard: true healthGoalUtils.js:73:10
🔍 getHealthGoalWithProgress() called for goalId: fitUmgebung healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749811128728", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811128728", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 4, challengeCompletions: {…} }
healthGoalUtils.js:40:10
🎯 Health goal fitUmgebung active status: true healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811128728", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:54:10
🎨 Generated card props: 
Object { cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811128728", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", pillColor: "--accent-blue", goalinfoIcon1: "http://localhost:1234/icon_moneypig.64d08e58.svg?1749811128728", … }
healthGoalUtils.js:108:10
📊 Progress display: 0 von 4 Challenges geschafft healthGoalUtils.js:109:10
Card props for active card: 
Object { cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811128728", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", pillColor: "--accent-blue", goalinfoIcon1: "http://localhost:1234/icon_moneypig.64d08e58.svg?1749811128728", … }
healthGoalUtils.js:400:10
📊 Step 5: Checking configuration healthGoalUtils.js:403:10
Health goal configuration: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749811128728", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811128728", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:406:10
📊 Step 6: Validating data consistency healthGoalUtils.js:409:10
Data consistency validation: 
Object { hasActiveGoal: true, activeGoalMatchesTarget: true, hasProgress: true, hasCardProps: true, hasConfig: true, progressDataConsistent: true }
healthGoalUtils.js:419:10
🔍 === DATA FLOW VERIFICATION SUMMARY === healthGoalUtils.js:431:10
✅ Data flow working: true healthGoalUtils.js:432:10
📊 Progress display: 0 von 4 Challenges geschafft healthGoalUtils.js:433:10
🔍 === DATA FLOW VERIFICATION END === healthGoalUtils.js:437:10
🔄 generateDynamicContent() called - checking for active health goal healthgoalOverview_Segment1.js:33:12
🔍 getActiveHealthGoal() called healthGoalUtils.js:119:10
📊 appStorage.activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:120:10
📊 appStorage._data.activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:121:10
🎯 Filtered active goals: 
Array [ "fitUmgebung" ]
healthGoalUtils.js:126:10
✅ Found active health goal: fitUmgebung healthGoalUtils.js:133:10
🔍 getHealthGoalWithProgress() called for goalId: fitUmgebung healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749811128728", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811128728", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 4, challengeCompletions: {…} }
healthGoalUtils.js:40:10

window.finalHealthGoalFix.fix()
🔧 Running final health goal fix... final_health_goal_fix.js:9:10
📊 Step 1: Ensuring health goal is active final_health_goal_fix.js:12:10
✅ Health goal already active final_health_goal_fix.js:17:12
📊 Step 2: Force regenerating template content final_health_goal_fix.js:21:10
✅ Found Segment1 element final_health_goal_fix.js:25:12
📊 Step 3: Force updating segmented control final_health_goal_fix.js:89:10
🔄 Updating segmented control content... final_health_goal_fix.js:92:12
🔄 updateSegmentContent called for: Segment1 segmentedControl.js:10:12
📋 Found segment: 
Object { id: "Segment1", title: "Ziele", content: healthgoalOverview_segment1Content()
 }
segmentedControl.js:12:12
🔄 Updating dynamic content for segment: Segment1 segmentedControl.js:15:14
📍 Content element for Segment1: 
<div id="Segment1" class="tabcontent" style="display: flex; flex-dire…y: visible; opacity: 1;">
segmentedControl.js:17:14
📍 Found content element for Segment1, updating... segmentedControl.js:20:16
🎯 Calling content function for Segment1... segmentedControl.js:24:18
🔄 healthgoalOverview_segment1Content() called - generating fresh template healthgoalOverview_Segment1.js:121:10
🔍 === DATA FLOW VERIFICATION START === healthGoalUtils.js:366:10
🎯 Tracing data flow for health goal: fitUmgebung healthGoalUtils.js:367:10
📊 Step 1: Checking appStorage raw data healthGoalUtils.js:376:10
Raw appStorage data: 
Object { activeHealthGoals: {…}, healthGoalProgress: {…}, activeHealthGoalsGetter: {…} }
healthGoalUtils.js:382:10
📊 Step 2: Testing getActiveHealthGoal() healthGoalUtils.js:385:10
🔍 getActiveHealthGoal() called healthGoalUtils.js:119:10
📊 appStorage.activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:120:10
📊 appStorage._data.activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:121:10
🎯 Filtered active goals: 
Array [ "fitUmgebung" ]
healthGoalUtils.js:126:10
✅ Found active health goal: fitUmgebung healthGoalUtils.js:133:10
🔍 getHealthGoalWithProgress() called for goalId: fitUmgebung healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749811128728", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811128728", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 4, challengeCompletions: {…} }
healthGoalUtils.js:40:10
🎯 Health goal fitUmgebung active status: true healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811128728", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:54:10
📈 Health goal with progress: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811128728", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:137:10
Active health goal result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811128728", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:388:10
📊 Step 3: Testing getHealthGoalWithProgress() healthGoalUtils.js:391:10
🔍 getHealthGoalWithProgress() called for goalId: fitUmgebung healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749811128728", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811128728", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 4, challengeCompletions: {…} }
healthGoalUtils.js:40:10
🎯 Health goal fitUmgebung active status: true healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811128728", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:54:10
Health goal with progress: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811128728", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:394:10
📊 Step 4: Testing getHealthGoalCardProps() healthGoalUtils.js:397:10
🎨 getHealthGoalCardProps() called for goalId: fitUmgebung, isActiveCard: true healthGoalUtils.js:73:10
🔍 getHealthGoalWithProgress() called for goalId: fitUmgebung healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749811128728", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811128728", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 4, challengeCompletions: {…} }
healthGoalUtils.js:40:10
🎯 Health goal fitUmgebung active status: true healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811128728", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:54:10
🎨 Generated card props: 
Object { cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811128728", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", pillColor: "--accent-blue", goalinfoIcon1: "http://localhost:1234/icon_moneypig.64d08e58.svg?1749811128728", … }
healthGoalUtils.js:108:10
📊 Progress display: 0 von 4 Challenges geschafft healthGoalUtils.js:109:10
Card props for active card: 
Object { cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811128728", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", pillColor: "--accent-blue", goalinfoIcon1: "http://localhost:1234/icon_moneypig.64d08e58.svg?1749811128728", … }
healthGoalUtils.js:400:10
📊 Step 5: Checking configuration healthGoalUtils.js:403:10
Health goal configuration: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749811128728", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811128728", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:406:10
📊 Step 6: Validating data consistency healthGoalUtils.js:409:10
Data consistency validation: 
Object { hasActiveGoal: true, activeGoalMatchesTarget: true, hasProgress: true, hasCardProps: true, hasConfig: true, progressDataConsistent: true }
healthGoalUtils.js:419:10
🔍 === DATA FLOW VERIFICATION SUMMARY === healthGoalUtils.js:431:10
✅ Data flow working: true healthGoalUtils.js:432:10
📊 Progress display: 0 von 4 Challenges geschafft healthGoalUtils.js:433:10
🔍 === DATA FLOW VERIFICATION END === healthGoalUtils.js:437:10
🔄 generateDynamicContent() called - checking for active health goal healthgoalOverview_Segment1.js:33:12
🔍 getActiveHealthGoal() called healthGoalUtils.js:119:10
📊 appStorage.activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:120:10
📊 appStorage._data.activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:121:10
🎯 Filtered active goals: 
Array [ "fitUmgebung" ]
healthGoalUtils.js:126:10
✅ Found active health goal: fitUmgebung healthGoalUtils.js:133:10
🔍 getHealthGoalWithProgress() called for goalId: fitUmgebung healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749811128728", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811128728", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 4, challengeCompletions: {…} }
healthGoalUtils.js:40:10
🎯 Health goal fitUmgebung active status: true healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811128728", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:54:10
📈 Health goal with progress: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811128728", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:137:10
📊 Active health goal result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811128728", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthgoalOverview_Segment1.js:37:12
🎨 getHealthGoalCardProps() called for goalId: fitUmgebung, isActiveCard: true healthGoalUtils.js:73:10
🔍 getHealthGoalWithProgress() called for goalId: fitUmgebung healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749811128728", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811128728", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 4, challengeCompletions: {…} }
healthGoalUtils.js:40:10

window.finalHealthGoalFix.test()
🧪 Testing health goal display... final_health_goal_fix.js:130:10
📊 Test results: final_health_goal_fix.js:144:10
  - Segment1 visible: true final_health_goal_fix.js:145:10
  - Has content: false final_health_goal_fix.js:146:10
  - Contains active goal: false final_health_goal_fix.js:147:10
  - Contains active status: false final_health_goal_fix.js:148:10
  - Contains progress: false final_health_goal_fix.js:149:10
⚠️ Some tests failed. Running fix... final_health_goal_fix.js:156:12
🔧 Running final health goal fix... final_health_goal_fix.js:9:10
📊 Step 1: Ensuring health goal is active final_health_goal_fix.js:12:10
✅ Health goal already active final_health_goal_fix.js:17:12
📊 Step 2: Force regenerating template content final_health_goal_fix.js:21:10
✅ Found Segment1 element final_health_goal_fix.js:25:12
📊 Step 3: Force updating segmented control final_health_goal_fix.js:89:10
🔄 Updating segmented control content... final_health_goal_fix.js:92:12
🔄 updateSegmentContent called for: Segment1 segmentedControl.js:10:12
📋 Found segment: 
Object { id: "Segment1", title: "Ziele", content: healthgoalOverview_segment1Content()
 }
segmentedControl.js:12:12
🔄 Updating dynamic content for segment: Segment1 segmentedControl.js:15:14
📍 Content element for Segment1: 
<div id="Segment1" class="tabcontent" style="display: flex; flex-dire…y: visible; opacity: 1;">
segmentedControl.js:17:14
📍 Found content element for Segment1, updating... segmentedControl.js:20:16
🎯 Calling content function for Segment1... segmentedControl.js:24:18
🔄 healthgoalOverview_segment1Content() called - generating fresh template healthgoalOverview_Segment1.js:121:10
🔍 === DATA FLOW VERIFICATION START === healthGoalUtils.js:366:10
🎯 Tracing data flow for health goal: fitUmgebung healthGoalUtils.js:367:10
📊 Step 1: Checking appStorage raw data healthGoalUtils.js:376:10
Raw appStorage data: 
Object { activeHealthGoals: {…}, healthGoalProgress: {…}, activeHealthGoalsGetter: {…} }
healthGoalUtils.js:382:10
📊 Step 2: Testing getActiveHealthGoal() healthGoalUtils.js:385:10
🔍 getActiveHealthGoal() called healthGoalUtils.js:119:10
📊 appStorage.activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:120:10
📊 appStorage._data.activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:121:10
🎯 Filtered active goals: 
Array [ "fitUmgebung" ]
healthGoalUtils.js:126:10
✅ Found active health goal: fitUmgebung healthGoalUtils.js:133:10
🔍 getHealthGoalWithProgress() called for goalId: fitUmgebung healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749811128728", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811128728", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 4, challengeCompletions: {…} }
healthGoalUtils.js:40:10
🎯 Health goal fitUmgebung active status: true healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811128728", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:54:10
📈 Health goal with progress: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811128728", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:137:10
Active health goal result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811128728", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:388:10
📊 Step 3: Testing getHealthGoalWithProgress() healthGoalUtils.js:391:10
🔍 getHealthGoalWithProgress() called for goalId: fitUmgebung healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749811128728", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811128728", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 4, challengeCompletions: {…} }
healthGoalUtils.js:40:10
🎯 Health goal fitUmgebung active status: true healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811128728", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:54:10
Health goal with progress: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811128728", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:394:10
📊 Step 4: Testing getHealthGoalCardProps() healthGoalUtils.js:397:10
🎨 getHealthGoalCardProps() called for goalId: fitUmgebung, isActiveCard: true healthGoalUtils.js:73:10
🔍 getHealthGoalWithProgress() called for goalId: fitUmgebung healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749811128728", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811128728", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 4, challengeCompletions: {…} }
healthGoalUtils.js:40:10
🎯 Health goal fitUmgebung active status: true healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811128728", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:54:10
🎨 Generated card props: 
Object { cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811128728", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", pillColor: "--accent-blue", goalinfoIcon1: "http://localhost:1234/icon_moneypig.64d08e58.svg?1749811128728", … }
healthGoalUtils.js:108:10
📊 Progress display: 0 von 4 Challenges geschafft healthGoalUtils.js:109:10
Card props for active card: 
Object { cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811128728", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", pillColor: "--accent-blue", goalinfoIcon1: "http://localhost:1234/icon_moneypig.64d08e58.svg?1749811128728", … }
healthGoalUtils.js:400:10
📊 Step 5: Checking configuration healthGoalUtils.js:403:10
Health goal configuration: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749811128728", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811128728", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:406:10
📊 Step 6: Validating data consistency healthGoalUtils.js:409:10
Data consistency validation: 
Object { hasActiveGoal: true, activeGoalMatchesTarget: true, hasProgress: true, hasCardProps: true, hasConfig: true, progressDataConsistent: true }
healthGoalUtils.js:419:10
🔍 === DATA FLOW VERIFICATION SUMMARY === healthGoalUtils.js:431:10
✅ Data flow working: true healthGoalUtils.js:432:10
📊 Progress display: 0 von 4 Challenges geschafft healthGoalUtils.js:433:10
🔍 === DATA FLOW VERIFICATION END === healthGoalUtils.js:437:10
🔄 generateDynamicContent() called - checking for active health goal healthgoalOverview_Segment1.js:33:12
🔍 getActiveHealthGoal() called healthGoalUtils.js:119:10
📊 appStorage.activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:120:10
📊 appStorage._data.activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:121:10
🎯 Filtered active goals: 
Array [ "fitUmgebung" ]
healthGoalUtils.js:126:10
✅ Found active health goal: fitUmgebung healthGoalUtils.js:133:10
🔍 getHealthGoalWithProgress() called for goalId: fitUmgebung healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749811128728", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811128728", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811128728", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 4, challengeCompletions: {…} }
healthGoalUtils.js:40:10
🎯 Health goal fitUmgebung active status: true healthGoalUtils.js:43:10
