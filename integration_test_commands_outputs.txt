window.healthGoalTests.quickHealthCheck()
🏥 Quick Health Check... integrationTestRunner.js:234:12
📊 AppStorage: ✅ integrationTestRunner.js:238:12
Health goal fitUmgebung activated healthGoalUtils.js:162:10
🎯 Health Goal Activation: ✅ integrationTestRunner.js:243:12
🔍 getActiveHealthGoal() called healthGoalUtils.js:119:10
📊 appStorage.activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:120:10
📊 appStorage._data.activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:121:10
🎯 Filtered active goals: 
Array [ "fitUmgebung" ]
healthGoalUtils.js:126:10
✅ Found active health goal: fitUmgebung healthGoalUtils.js:133:10
🔍 getHealthGoalWithProgress() called for goalId: fitUmgebung healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749808819005", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808819005", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749808819005", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 4, challengeCompletions: {} }
healthGoalUtils.js:40:10
🎯 Health goal fitUmgebung active status: true healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808819005", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808819005", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749808819005", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:54:10
📈 Health goal with progress: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808819005", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808819005", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749808819005", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:137:10
🔍 Active Goal Retrieval: ✅ integrationTestRunner.js:248:12
🏥 Overall Health: ✅ HEALTHY integrationTestRunner.js:251:12
true 

window.healthGoalTests.validateBugFixes()
🐛 === BUG FIX VALIDATION === integrationTestRunner.js:438:10
🔍 Testing Issue 1: Active Health Goal Detection Bug integrationTestRunner.js:447:10
Health goal fitUmgebung activated healthGoalUtils.js:162:10
🔍 getActiveHealthGoal() called healthGoalUtils.js:119:10
📊 appStorage.activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:120:10
📊 appStorage._data.activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:121:10
🎯 Filtered active goals: 
Array [ "fitUmgebung" ]
healthGoalUtils.js:126:10
✅ Found active health goal: fitUmgebung healthGoalUtils.js:133:10
🔍 getHealthGoalWithProgress() called for goalId: fitUmgebung healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749809252310", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809252310", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809252310", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 4, challengeCompletions: {} }
healthGoalUtils.js:40:10
🎯 Health goal fitUmgebung active status: true healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809252310", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809252310", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809252310", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:54:10
📈 Health goal with progress: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809252310", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809252310", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809252310", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:137:10
Issue 1: ✅ FIXED integrationTestRunner.js:466:12
🔍 Testing Issue 2: Health Goal Activation After Niveau-Poll integrationTestRunner.js:477:10
App-Daten zurückgesetzt utils.js:434:12
Health Goal Progress aus Konfiguration initialisiert utils.js:412:12
Health Goal System initialized healthGoalUtils.js:21:10
Healthgoal-Niveau gesetzt: beginner utils.js:304:12
Issue 2: ✅ FIXED integrationTestRunner.js:498:12
🔍 Testing Issue 3: Data Connection Verification integrationTestRunner.js:509:10
🔍 === DATA FLOW VERIFICATION START === healthGoalUtils.js:366:10
🎯 Tracing data flow for health goal: fitUmgebung healthGoalUtils.js:367:10
📊 Step 1: Checking appStorage raw data healthGoalUtils.js:376:10
Raw appStorage data: 
Object { activeHealthGoals: {…}, healthGoalProgress: {…}, activeHealthGoalsGetter: {…} }
healthGoalUtils.js:382:10
📊 Step 2: Testing getActiveHealthGoal() healthGoalUtils.js:385:10
🔍 getActiveHealthGoal() called healthGoalUtils.js:119:10
📊 appStorage.activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:120:10
📊 appStorage._data.activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:121:10
🎯 Filtered active goals: 
Array [ "fitUmgebung" ]
healthGoalUtils.js:126:10
✅ Found active health goal: fitUmgebung healthGoalUtils.js:133:10
🔍 getHealthGoalWithProgress() called for goalId: fitUmgebung healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749809252310", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809252310", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809252310", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 4, challengeCompletions: {} }
healthGoalUtils.js:40:10
🎯 Health goal fitUmgebung active status: true healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809252310", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809252310", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809252310", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:54:10
📈 Health goal with progress: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809252310", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809252310", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809252310", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:137:10
Active health goal result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809252310", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809252310", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809252310", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:388:10
📊 Step 3: Testing getHealthGoalWithProgress() healthGoalUtils.js:391:10
🔍 getHealthGoalWithProgress() called for goalId: fitUmgebung healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749809252310", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809252310", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809252310", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 4, challengeCompletions: {} }
healthGoalUtils.js:40:10
🎯 Health goal fitUmgebung active status: true healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809252310", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809252310", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809252310", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:54:10
Health goal with progress: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809252310", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809252310", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809252310", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:394:10
📊 Step 4: Testing getHealthGoalCardProps() healthGoalUtils.js:397:10
🎨 getHealthGoalCardProps() called for goalId: fitUmgebung, isActiveCard: true healthGoalUtils.js:73:10
🔍 getHealthGoalWithProgress() called for goalId: fitUmgebung healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749809252310", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809252310", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809252310", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 4, challengeCompletions: {} }
healthGoalUtils.js:40:10
🎯 Health goal fitUmgebung active status: true healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809252310", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809252310", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809252310", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:54:10
🎨 Generated card props: 
Object { cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809252310", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809252310", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", pillColor: "--accent-blue", goalinfoIcon1: "http://localhost:1234/icon_moneypig.64d08e58.svg?1749809252310", … }
healthGoalUtils.js:108:10
📊 Progress display: 0 von 4 Challenges geschafft healthGoalUtils.js:109:10
Card props for active card: 
Object { cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809252310", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809252310", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", pillColor: "--accent-blue", goalinfoIcon1: "http://localhost:1234/icon_moneypig.64d08e58.svg?1749809252310", … }
healthGoalUtils.js:400:10
📊 Step 5: Checking configuration healthGoalUtils.js:403:10
Health goal configuration: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749809252310", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809252310", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809252310", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:406:10
📊 Step 6: Validating data consistency healthGoalUtils.js:409:10
Data consistency validation: 
Object { hasActiveGoal: true, activeGoalMatchesTarget: true, hasProgress: true, hasCardProps: true, hasConfig: true, progressDataConsistent: true }
healthGoalUtils.js:419:10
🔍 === DATA FLOW VERIFICATION SUMMARY === healthGoalUtils.js:431:10
✅ Data flow working: true healthGoalUtils.js:432:10
📊 Progress display: 0 von 4 Challenges geschafft healthGoalUtils.js:433:10
🔍 === DATA FLOW VERIFICATION END === healthGoalUtils.js:437:10
Issue 3: ✅ FIXED integrationTestRunner.js:520:12
🔍 Testing Issue 4: Template Rendering Fix integrationTestRunner.js:531:10
❌ Issue 4: ERROR - healthgoalOverview_segment1Content is not defined integrationTestRunner.js:561:12
🔍 Testing Issue 5: CSS Layout Fix integrationTestRunner.js:565:10
Issue 5: ✅ FIXED integrationTestRunner.js:581:12
🐛 === BUG FIX VALIDATION SUMMARY === integrationTestRunner.js:595:10
✅ Fixed: 4/5 integrationTestRunner.js:596:10
📊 Success Rate: 80% integrationTestRunner.js:597:10
⚠️ Most bugs fixed, but some issues remain. integrationTestRunner.js:602:12
Object { timestamp: "2025-06-13T10:07:47.681Z", issues: {…}, summary: {…} }

window.healthGoalTests.testCompleteUserJourney()
🚀 === COMPLETE USER JOURNEY TEST === integrationTestRunner.js:316:10
📊 Step 1: Simulating niveau-poll completion... integrationTestRunner.js:327:12
App-Daten zurückgesetzt utils.js:434:12
Health Goal Progress aus Konfiguration initialisiert utils.js:412:12
Health Goal System initialized healthGoalUtils.js:21:10
Healthgoal-Niveau gesetzt: beginner utils.js:304:12
📊 Step 2: Testing active health goal detection... integrationTestRunner.js:344:12
🔍 getActiveHealthGoal() called healthGoalUtils.js:119:10
📊 appStorage.activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:120:10
📊 appStorage._data.activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:121:10
🎯 Filtered active goals: 
Array [ "fitUmgebung" ]
healthGoalUtils.js:126:10
✅ Found active health goal: fitUmgebung healthGoalUtils.js:133:10
🔍 getHealthGoalWithProgress() called for goalId: fitUmgebung healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749808819005", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808819005", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749808819005", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 4, challengeCompletions: {} }
healthGoalUtils.js:40:10
🎯 Health goal fitUmgebung active status: true healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808819005", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808819005", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749808819005", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:54:10
📈 Health goal with progress: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808819005", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808819005", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749808819005", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:137:10
📊 Step 3: Testing template generation... integrationTestRunner.js:359:12
📊 Step 4: Testing progress data... integrationTestRunner.js:386:12
🚀 === COMPLETE USER JOURNEY TEST SUMMARY === integrationTestRunner.js:423:10
✅ Journey test FAILED integrationTestRunner.js:424:10
❌ Errors: Journey test error: getHealthGoalCardProps is not defined integrationTestRunner.js:426:12
🚀 === COMPLETE USER JOURNEY TEST END === integrationTestRunner.js:428:10
🔄 healthgoalOverview_segment1Content() called - generating fresh template healthgoalOverview_Segment1.js:119:10
🔍 === DATA FLOW VERIFICATION START === healthGoalUtils.js:366:10
🎯 Tracing data flow for health goal: fitUmgebung healthGoalUtils.js:367:10
📊 Step 1: Checking appStorage raw data healthGoalUtils.js:376:10
Raw appStorage data: 
Object { activeHealthGoals: {…}, healthGoalProgress: {…}, activeHealthGoalsGetter: {…} }
healthGoalUtils.js:382:10
📊 Step 2: Testing getActiveHealthGoal() healthGoalUtils.js:385:10
🔍 getActiveHealthGoal() called healthGoalUtils.js:119:10
📊 appStorage.activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:120:10
📊 appStorage._data.activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:121:10
🎯 Filtered active goals: 
Array [ "fitUmgebung" ]
healthGoalUtils.js:126:10
✅ Found active health goal: fitUmgebung healthGoalUtils.js:133:10
🔍 getHealthGoalWithProgress() called for goalId: fitUmgebung healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749808819005", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808819005", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749808819005", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 4, challengeCompletions: {} }
healthGoalUtils.js:40:10
🎯 Health goal fitUmgebung active status: true healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808819005", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808819005", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749808819005", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:54:10
📈 Health goal with progress: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808819005", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808819005", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749808819005", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:137:10
Active health goal result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808819005", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808819005", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749808819005", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:388:10
📊 Step 3: Testing getHealthGoalWithProgress() healthGoalUtils.js:391:10
🔍 getHealthGoalWithProgress() called for goalId: fitUmgebung healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749808819005", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808819005", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749808819005", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 4, challengeCompletions: {} }
healthGoalUtils.js:40:10
🎯 Health goal fitUmgebung active status: true healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808819005", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808819005", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749808819005", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:54:10
Health goal with progress: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808819005", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808819005", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749808819005", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:394:10
📊 Step 4: Testing getHealthGoalCardProps() healthGoalUtils.js:397:10
🎨 getHealthGoalCardProps() called for goalId: fitUmgebung, isActiveCard: true healthGoalUtils.js:73:10
🔍 getHealthGoalWithProgress() called for goalId: fitUmgebung healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749808819005", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808819005", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749808819005", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 4, challengeCompletions: {} }
healthGoalUtils.js:40:10
🎯 Health goal fitUmgebung active status: true healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:44:10

window.healthGoalTests.runAllTests()
🚀 === DYNAMIC HEALTH GOALS SYSTEM - FULL INTEGRATION TEST === integrationTestRunner.js:34:12
🕐 Started at: 2025-06-13T10:04:07.142Z integrationTestRunner.js:35:12

🔍 Running System Validation... integrationTestRunner.js:56:25

🔄 Running Data Flow Verification... integrationTestRunner.js:66:25
🔍 === DATA FLOW VERIFICATION START === healthGoalUtils.js:366:10
🎯 Tracing data flow for health goal: fitUmgebung healthGoalUtils.js:367:10
📊 Step 1: Checking appStorage raw data healthGoalUtils.js:376:10
Raw appStorage data: 
Object { activeHealthGoals: {…}, healthGoalProgress: {…}, activeHealthGoalsGetter: {…} }
healthGoalUtils.js:382:10
📊 Step 2: Testing getActiveHealthGoal() healthGoalUtils.js:385:10
🔍 getActiveHealthGoal() called healthGoalUtils.js:119:10
📊 appStorage.activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:120:10
📊 appStorage._data.activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:121:10
🎯 Filtered active goals: 
Array [ "fitUmgebung" ]
healthGoalUtils.js:126:10
✅ Found active health goal: fitUmgebung healthGoalUtils.js:133:10
🔍 getHealthGoalWithProgress() called for goalId: fitUmgebung healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749808819005", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808819005", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749808819005", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 4, challengeCompletions: {} }
healthGoalUtils.js:40:10
🎯 Health goal fitUmgebung active status: true healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808819005", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808819005", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749808819005", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:54:10
📈 Health goal with progress: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808819005", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808819005", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749808819005", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:137:10
Active health goal result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808819005", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808819005", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749808819005", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:388:10
📊 Step 3: Testing getHealthGoalWithProgress() healthGoalUtils.js:391:10
🔍 getHealthGoalWithProgress() called for goalId: fitUmgebung healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749808819005", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808819005", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749808819005", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 4, challengeCompletions: {} }
healthGoalUtils.js:40:10
🎯 Health goal fitUmgebung active status: true healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808819005", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808819005", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749808819005", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:54:10
Health goal with progress: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808819005", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808819005", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749808819005", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:394:10
📊 Step 4: Testing getHealthGoalCardProps() healthGoalUtils.js:397:10
🎨 getHealthGoalCardProps() called for goalId: fitUmgebung, isActiveCard: true healthGoalUtils.js:73:10
🔍 getHealthGoalWithProgress() called for goalId: fitUmgebung healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749808819005", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808819005", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749808819005", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 4, challengeCompletions: {} }
healthGoalUtils.js:40:10
🎯 Health goal fitUmgebung active status: true healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808819005", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808819005", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749808819005", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:54:10
🎨 Generated card props: 
Object { cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808819005", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749808819005", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", pillColor: "--accent-blue", goalinfoIcon1: "http://localhost:1234/icon_moneypig.64d08e58.svg?1749808819005", … }
healthGoalUtils.js:108:10
📊 Progress display: 0 von 4 Challenges geschafft healthGoalUtils.js:109:10
Card props for active card: 
Object { cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808819005", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749808819005", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", pillColor: "--accent-blue", goalinfoIcon1: "http://localhost:1234/icon_moneypig.64d08e58.svg?1749808819005", … }
healthGoalUtils.js:400:10
📊 Step 5: Checking configuration healthGoalUtils.js:403:10
Health goal configuration: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749808819005", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808819005", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749808819005", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:406:10
📊 Step 6: Validating data consistency healthGoalUtils.js:409:10
Data consistency validation: 
Object { hasActiveGoal: true, activeGoalMatchesTarget: true, hasProgress: true, hasCardProps: true, hasConfig: true, progressDataConsistent: true }
healthGoalUtils.js:419:10
🔍 === DATA FLOW VERIFICATION SUMMARY === healthGoalUtils.js:431:10
✅ Data flow working: true healthGoalUtils.js:432:10
📊 Progress display: 0 von 4 Challenges geschafft healthGoalUtils.js:433:10
🔍 === DATA FLOW VERIFICATION END === healthGoalUtils.js:437:10

🧪 Running Integration Tests... integrationTestRunner.js:76:25
🧪 === INTEGRATION TESTING START === healthGoalUtils.js:448:10
✅ Health Goal Activation: PASSED healthGoalUtils.js:462:16
🔍 getActiveHealthGoal() called healthGoalUtils.js:119:10
📊 appStorage.activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:120:10
📊 appStorage._data.activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:121:10
🎯 Filtered active goals: 
Array [ "fitUmgebung" ]
healthGoalUtils.js:126:10
✅ Found active health goal: fitUmgebung healthGoalUtils.js:133:10
🔍 getHealthGoalWithProgress() called for goalId: fitUmgebung healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749808819005", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808819005", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749808819005", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 4, challengeCompletions: {} }
healthGoalUtils.js:40:10
🎯 Health goal fitUmgebung active status: true healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808819005", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808819005", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749808819005", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:54:10
📈 Health goal with progress: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808819005", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749808819005", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749808819005", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:137:10
✅ Active Health Goal Detection: PASSED healthGoalUtils.js:462:16
✅ Health Goal Configuration Loading: PASSED healthGoalUtils.js:462:16
