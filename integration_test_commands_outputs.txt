window.healthGoalDisplayFix.fix()
🔧 Starting health goal display fix... fix_health_goal_display.js:150:10
undefined
🔍 Checking health goal display... fix_health_goal_display.js:93:10
📊 Health goal active: true fix_health_goal_display.js:97:10
📊 Segmented control container: true fix_health_goal_display.js:106:10
📊 Number of tabs found: 2 fix_health_goal_display.js:111:12
📊 Number of tab content areas found: 2 fix_health_goal_display.js:115:12
📊 Segment1 visible: true fix_health_goal_display.js:121:14
✅ Segment1 is visible, checking content... fix_health_goal_display.js:127:16
✅ Segment1 contains active health goal content fix_health_goal_display.js:137:18
🔍 Checking health goal display... fix_health_goal_display.js:93:10
📊 Health goal active: true fix_health_goal_display.js:97:10
📊 Segmented control container: true fix_health_goal_display.js:106:10
📊 Number of tabs found: 2 fix_health_goal_display.js:111:12
📊 Number of tab content areas found: 2 fix_health_goal_display.js:115:12
📊 Segment1 visible: true fix_health_goal_display.js:121:14
✅ Segment1 is visible, checking content... fix_health_goal_display.js:127:16
✅ Segment1 contains active health goal content fix_health_goal_display.js:137:18
🔍 Checking health goal display... fix_health_goal_display.js:93:10
📊 Health goal active: true fix_health_goal_display.js:97:10
📊 Segmented control container: true fix_health_goal_display.js:106:10
📊 Number of tabs found: 2 fix_health_goal_display.js:111:12
📊 Number of tab content areas found: 2 fix_health_goal_display.js:115:12
📊 Segment1 visible: true fix_health_goal_display.js:121:14
✅ Segment1 is visible, checking content... fix_health_goal_display.js:127:16
✅ Segment1 contains active health goal content fix_health_goal_display.js:137:18

window.healthGoalDisplayFix.forceUpdate()
🔄 Force updating health goal display... fix_health_goal_display.js:10:10
✅ Found Segment1 element fix_health_goal_display.js:21:12
🔄 Updating Segment1 content with fresh template... fix_health_goal_display.js:28:14
🔄 healthgoalOverview_segment1Content() called - generating fresh template healthgoalOverview_Segment1.js:119:10
🔍 === DATA FLOW VERIFICATION START === healthGoalUtils.js:366:10
🎯 Tracing data flow for health goal: fitUmgebung healthGoalUtils.js:367:10
📊 Step 1: Checking appStorage raw data healthGoalUtils.js:376:10
Raw appStorage data: 
Object { activeHealthGoals: {…}, healthGoalProgress: {…}, activeHealthGoalsGetter: {…} }
healthGoalUtils.js:382:10
📊 Step 2: Testing getActiveHealthGoal() healthGoalUtils.js:385:10
🔍 getActiveHealthGoal() called healthGoalUtils.js:119:10
📊 appStorage.activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:120:10
📊 appStorage._data.activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:121:10
🎯 Filtered active goals: 
Array [ "fitUmgebung" ]
healthGoalUtils.js:126:10
✅ Found active health goal: fitUmgebung healthGoalUtils.js:133:10
🔍 getHealthGoalWithProgress() called for goalId: fitUmgebung healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749809865657", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809865657", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 4, challengeCompletions: {…} }
healthGoalUtils.js:40:10
🎯 Health goal fitUmgebung active status: true healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809865657", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:54:10
📈 Health goal with progress: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809865657", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:137:10
Active health goal result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809865657", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:388:10
📊 Step 3: Testing getHealthGoalWithProgress() healthGoalUtils.js:391:10
🔍 getHealthGoalWithProgress() called for goalId: fitUmgebung healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749809865657", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809865657", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 4, challengeCompletions: {…} }
healthGoalUtils.js:40:10
🎯 Health goal fitUmgebung active status: true healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809865657", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:54:10
Health goal with progress: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809865657", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:394:10
📊 Step 4: Testing getHealthGoalCardProps() healthGoalUtils.js:397:10
🎨 getHealthGoalCardProps() called for goalId: fitUmgebung, isActiveCard: true healthGoalUtils.js:73:10
🔍 getHealthGoalWithProgress() called for goalId: fitUmgebung healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749809865657", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809865657", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 4, challengeCompletions: {…} }
healthGoalUtils.js:40:10
🎯 Health goal fitUmgebung active status: true healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809865657", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:54:10
🎨 Generated card props: 
Object { cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809865657", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", pillColor: "--accent-blue", goalinfoIcon1: "http://localhost:1234/icon_moneypig.64d08e58.svg?1749809865657", … }
healthGoalUtils.js:108:10
📊 Progress display: 0 von 4 Challenges geschafft healthGoalUtils.js:109:10
Card props for active card: 
Object { cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809865657", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", pillColor: "--accent-blue", goalinfoIcon1: "http://localhost:1234/icon_moneypig.64d08e58.svg?1749809865657", … }
healthGoalUtils.js:400:10
📊 Step 5: Checking configuration healthGoalUtils.js:403:10
Health goal configuration: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749809865657", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809865657", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:406:10
📊 Step 6: Validating data consistency healthGoalUtils.js:409:10
Data consistency validation: 
Object { hasActiveGoal: true, activeGoalMatchesTarget: true, hasProgress: true, hasCardProps: true, hasConfig: true, progressDataConsistent: true }
healthGoalUtils.js:419:10
🔍 === DATA FLOW VERIFICATION SUMMARY === healthGoalUtils.js:431:10
✅ Data flow working: true healthGoalUtils.js:432:10
📊 Progress display: 0 von 4 Challenges geschafft healthGoalUtils.js:433:10
🔍 === DATA FLOW VERIFICATION END === healthGoalUtils.js:437:10
🔄 generateDynamicContent() called - checking for active health goal healthgoalOverview_Segment1.js:33:12
🔍 getActiveHealthGoal() called healthGoalUtils.js:119:10
📊 appStorage.activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:120:10
📊 appStorage._data.activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:121:10
🎯 Filtered active goals: 
Array [ "fitUmgebung" ]
healthGoalUtils.js:126:10
✅ Found active health goal: fitUmgebung healthGoalUtils.js:133:10
🔍 getHealthGoalWithProgress() called for goalId: fitUmgebung healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749809865657", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809865657", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 4, challengeCompletions: {…} }
healthGoalUtils.js:40:10
🎯 Health goal fitUmgebung active status: true healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809865657", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:54:10
📈 Health goal with progress: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809865657", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:137:10
📊 Active health goal result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809865657", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthgoalOverview_Segment1.js:37:12
🎨 getHealthGoalCardProps() called for goalId: fitUmgebung, isActiveCard: true healthGoalUtils.js:73:10
🔍 getHealthGoalWithProgress() called for goalId: fitUmgebung healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749809865657", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809865657", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 4, challengeCompletions: {…} }
healthGoalUtils.js:40:10
🎯 Health goal fitUmgebung active status: true healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809865657", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:54:10
🎨 Generated card props: 
Object { cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809865657", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", pillColor: "--accent-blue", goalinfoIcon1: "http://localhost:1234/icon_moneypig.64d08e58.svg?1749809865657", … }
healthGoalUtils.js:108:10
📊 Progress display: 0 von 4 Challenges geschafft healthGoalUtils.js:109:10
🔍 getHealthGoalWithProgress() called for goalId: fitUmgebung healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749809865657", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809865657", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 4, challengeCompletions: {…} }
healthGoalUtils.js:40:10
🎯 Health goal fitUmgebung active status: true healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:44:10
undefined

window.healthGoalDisplayFix.initializeControl()
🚀 Initializing segmented control... fix_health_goal_display.js:53:10
✅ Found first tab, activating... fix_health_goal_display.js:58:12
✅ Activated first tab content fix_health_goal_display.js:80:14
undefined
🔄 Force updating health goal display... fix_health_goal_display.js:10:10
✅ Found Segment1 element fix_health_goal_display.js:21:12
🔄 Updating Segment1 content with fresh template... fix_health_goal_display.js:28:14
🔄 healthgoalOverview_segment1Content() called - generating fresh template healthgoalOverview_Segment1.js:119:10
🔍 === DATA FLOW VERIFICATION START === healthGoalUtils.js:366:10
🎯 Tracing data flow for health goal: fitUmgebung healthGoalUtils.js:367:10
📊 Step 1: Checking appStorage raw data healthGoalUtils.js:376:10
Raw appStorage data: 
Object { activeHealthGoals: {…}, healthGoalProgress: {…}, activeHealthGoalsGetter: {…} }
healthGoalUtils.js:382:10
📊 Step 2: Testing getActiveHealthGoal() healthGoalUtils.js:385:10
🔍 getActiveHealthGoal() called healthGoalUtils.js:119:10
📊 appStorage.activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:120:10
📊 appStorage._data.activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:121:10
🎯 Filtered active goals: 
Array [ "fitUmgebung" ]
healthGoalUtils.js:126:10
✅ Found active health goal: fitUmgebung healthGoalUtils.js:133:10
🔍 getHealthGoalWithProgress() called for goalId: fitUmgebung healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749809865657", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809865657", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 4, challengeCompletions: {…} }
healthGoalUtils.js:40:10
🎯 Health goal fitUmgebung active status: true healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809865657", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:54:10
📈 Health goal with progress: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809865657", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:137:10
Active health goal result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809865657", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:388:10
📊 Step 3: Testing getHealthGoalWithProgress() healthGoalUtils.js:391:10
🔍 getHealthGoalWithProgress() called for goalId: fitUmgebung healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749809865657", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809865657", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 4, challengeCompletions: {…} }
healthGoalUtils.js:40:10
🎯 Health goal fitUmgebung active status: true healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809865657", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:54:10
Health goal with progress: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809865657", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:394:10
📊 Step 4: Testing getHealthGoalCardProps() healthGoalUtils.js:397:10
🎨 getHealthGoalCardProps() called for goalId: fitUmgebung, isActiveCard: true healthGoalUtils.js:73:10
🔍 getHealthGoalWithProgress() called for goalId: fitUmgebung healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749809865657", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809865657", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 4, challengeCompletions: {…} }
healthGoalUtils.js:40:10
🎯 Health goal fitUmgebung active status: true healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809865657", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:54:10
🎨 Generated card props: 
Object { cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809865657", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", pillColor: "--accent-blue", goalinfoIcon1: "http://localhost:1234/icon_moneypig.64d08e58.svg?1749809865657", … }
healthGoalUtils.js:108:10
📊 Progress display: 0 von 4 Challenges geschafft healthGoalUtils.js:109:10
Card props for active card: 
Object { cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809865657", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", pillColor: "--accent-blue", goalinfoIcon1: "http://localhost:1234/icon_moneypig.64d08e58.svg?1749809865657", … }
healthGoalUtils.js:400:10
📊 Step 5: Checking configuration healthGoalUtils.js:403:10
Health goal configuration: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749809865657", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809865657", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:406:10
📊 Step 6: Validating data consistency healthGoalUtils.js:409:10
Data consistency validation: 
Object { hasActiveGoal: true, activeGoalMatchesTarget: true, hasProgress: true, hasCardProps: true, hasConfig: true, progressDataConsistent: true }
healthGoalUtils.js:419:10
🔍 === DATA FLOW VERIFICATION SUMMARY === healthGoalUtils.js:431:10
✅ Data flow working: true healthGoalUtils.js:432:10
📊 Progress display: 0 von 4 Challenges geschafft healthGoalUtils.js:433:10
🔍 === DATA FLOW VERIFICATION END === healthGoalUtils.js:437:10
🔄 generateDynamicContent() called - checking for active health goal healthgoalOverview_Segment1.js:33:12
🔍 getActiveHealthGoal() called healthGoalUtils.js:119:10
📊 appStorage.activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:120:10
📊 appStorage._data.activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:121:10
🎯 Filtered active goals: 
Array [ "fitUmgebung" ]
healthGoalUtils.js:126:10
✅ Found active health goal: fitUmgebung healthGoalUtils.js:133:10
🔍 getHealthGoalWithProgress() called for goalId: fitUmgebung healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749809865657", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809865657", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 4, challengeCompletions: {…} }
healthGoalUtils.js:40:10
🎯 Health goal fitUmgebung active status: true healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809865657", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:54:10
📈 Health goal with progress: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809865657", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:137:10
📊 Active health goal result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809865657", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthgoalOverview_Segment1.js:37:12
🎨 getHealthGoalCardProps() called for goalId: fitUmgebung, isActiveCard: true healthGoalUtils.js:73:10
🔍 getHealthGoalWithProgress() called for goalId: fitUmgebung healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749809865657", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809865657", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 4, challengeCompletions: {…} }
healthGoalUtils.js:40:10
🎯 Health goal fitUmgebung active status: true healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809865657", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:54:10
🎨 Generated card props: 
Object { cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749809865657", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749809865657", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", pillColor: "--accent-blue", goalinfoIcon1: "http://localhost:1234/icon_moneypig.64d08e58.svg?1749809865657", … }
healthGoalUtils.js:108:10
📊 Progress display: 0 von 4 Challenges geschafft healthGoalUtils.js:109:10
🔍 getHealthGoalWithProgress() called for goalId: fitUmgebung healthGoalUtils.js:30:10
👁️ Made Segment1 visible fix_health_goal_display.js:41:14

