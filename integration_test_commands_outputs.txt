window.simpleHealthGoalFix.fix()
🔧 Running simple health goal fix... simple_health_goal_fix.js:9:10
📊 Getting active health goal data... simple_health_goal_fix.js:18:10
🔍 getActiveHealthGoal() called healthGoalUtils.js:119:10
📊 appStorage.activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:120:10
📊 appStorage._data.activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:121:10
🎯 Filtered active goals: 
Array [ "fitUmgebung" ]
healthGoalUtils.js:126:10
✅ Found active health goal: fitUmgebung healthGoalUtils.js:133:10
🔍 getHealthGoalWithProgress() called for goalId: fitUmgebung healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749811649746", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811649746", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811649746", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 4, challengeCompletions: {…} }
healthGoalUtils.js:40:10
🎯 Health goal fitUmgebung active status: true healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811649746", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811649746", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811649746", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:54:10
📈 Health goal with progress: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811649746", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811649746", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811649746", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:137:10
✅ Active goal: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811649746", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811649746", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811649746", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
simple_health_goal_fix.js:24:12
🎨 getHealthGoalCardProps() called for goalId: fitUmgebung, isActiveCard: true healthGoalUtils.js:73:10
🔍 getHealthGoalWithProgress() called for goalId: fitUmgebung healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749811649746", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811649746", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811649746", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 4, challengeCompletions: {…} }
healthGoalUtils.js:40:10
🎯 Health goal fitUmgebung active status: true healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811649746", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811649746", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811649746", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:54:10
🎨 Generated card props: 
Object { cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811649746", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811649746", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", pillColor: "--accent-blue", goalinfoIcon1: "http://localhost:1234/icon_moneypig.64d08e58.svg?1749811649746", … }
healthGoalUtils.js:108:10
📊 Progress display: 0 von 4 Challenges geschafft healthGoalUtils.js:109:10
✅ Card props: 
Object { cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811649746", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811649746", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", pillColor: "--accent-blue", goalinfoIcon1: "http://localhost:1234/icon_moneypig.64d08e58.svg?1749811649746", … }
simple_health_goal_fix.js:32:12
🎨 Creating HTML content directly... simple_health_goal_fix.js:40:12
✅ Found Segment1, inserting HTML... simple_health_goal_fix.js:128:14
🎉 SUCCESS! Active health goal HTML inserted directly! simple_health_goal_fix.js:137:14
undefined
📊 Final test results: simple_health_goal_fix.js:145:16
  - Contains "Fit in deiner Umgebung": true simple_health_goal_fix.js:146:16
  - Contains "Gerade aktiv": true simple_health_goal_fix.js:147:16
  - Contains progress text: true simple_health_goal_fix.js:148:16
🎉 PERFECT! All content is now visible! simple_health_goal_fix.js:151:18
🔄 Opening segment: Segment2 segmentedControl.js:113:12
🔄 updateSegmentContent called for: Segment2 segmentedControl.js:10:12
📋 Found segment: 
Object { id: "Segment2", title: "Bonuspunkte", content: {…} }
segmentedControl.js:12:12
📄 Segment Segment2 has static content or not found segmentedControl.js:79:14
📄 Segment content type: object segmentedControl.js:80:14
🔄 Opening segment: Segment1 segmentedControl.js:113:12
🔄 updateSegmentContent called for: Segment1 segmentedControl.js:10:12
📋 Found segment: 
Object { id: "Segment1", title: "Ziele", content: healthgoalOverview_segment1Content()
 }
segmentedControl.js:12:12
🔄 Updating dynamic content for segment: Segment1 segmentedControl.js:15:14
📍 Content element for Segment1: 
<div id="Segment1" class="tabcontent" style="display: none; flex-dire…y: visible; opacity: 1;">
segmentedControl.js:17:14
📍 Found content element for Segment1, updating... segmentedControl.js:20:16
🎯 Calling content function for Segment1... segmentedControl.js:24:18
🔄 healthgoalOverview_segment1Content() called - generating fresh template healthgoalOverview_Segment1.js:121:10
🔍 === DATA FLOW VERIFICATION START === healthGoalUtils.js:366:10
🎯 Tracing data flow for health goal: fitUmgebung healthGoalUtils.js:367:10
📊 Step 1: Checking appStorage raw data healthGoalUtils.js:376:10
Raw appStorage data: 
Object { activeHealthGoals: {…}, healthGoalProgress: {…}, activeHealthGoalsGetter: {…} }
healthGoalUtils.js:382:10
📊 Step 2: Testing getActiveHealthGoal() healthGoalUtils.js:385:10
🔍 getActiveHealthGoal() called healthGoalUtils.js:119:10
📊 appStorage.activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:120:10
📊 appStorage._data.activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:121:10
🎯 Filtered active goals: 
Array [ "fitUmgebung" ]
healthGoalUtils.js:126:10
✅ Found active health goal: fitUmgebung healthGoalUtils.js:133:10
🔍 getHealthGoalWithProgress() called for goalId: fitUmgebung healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749811649746", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811649746", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811649746", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 4, challengeCompletions: {…} }
healthGoalUtils.js:40:10
🎯 Health goal fitUmgebung active status: true healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811649746", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811649746", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811649746", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:54:10
📈 Health goal with progress: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811649746", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811649746", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811649746", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:137:10
Active health goal result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811649746", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811649746", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811649746", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:388:10
📊 Step 3: Testing getHealthGoalWithProgress() healthGoalUtils.js:391:10
🔍 getHealthGoalWithProgress() called for goalId: fitUmgebung healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749811649746", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811649746", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811649746", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:33:10
📈 Health goal progress: 
Object { completedChallenges: 0, totalChallenges: 4, challengeCompletions: {…} }
healthGoalUtils.js:40:10
🎯 Health goal fitUmgebung active status: true healthGoalUtils.js:43:10
📊 All activeHealthGoals: 
Object { fitUmgebung: true }
healthGoalUtils.js:44:10
✅ Final health goal with progress result: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811649746", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811649746", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811649746", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:54:10
Health goal with progress: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811649746", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811649746", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811649746", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:394:10
📊 Step 4: Testing getHealthGoalCardProps() healthGoalUtils.js:397:10
🎨 getHealthGoalCardProps() called for goalId: fitUmgebung, isActiveCard: true healthGoalUtils.js:73:10
🔍 getHealthGoalWithProgress() called for goalId: fitUmgebung healthGoalUtils.js:30:10
📋 Health goal config: 
Object { id: "fitUmgebung", cardImage: "http://localhost:1234/komoot_portrait.995b898f.png?1749811649746", cardImageActive: "http://localhost:1234/komoot_hg.f3ea4d30.jpg?1749811649746", tagCategory: "Bewegung", tagIcon: "http://localhost:1234/icon_tag_sport.e62f8640.svg?1749811649746", tagColor: "--tag-sport", tagTextColor: "--tag-text", healthgoalTitle: "Fit in deiner Umgebung", healthgoalCoop: "In Kooperation mit Komoot", pillText: "Gerade aktiv", … }
healthGoalUtils.js:33:10
