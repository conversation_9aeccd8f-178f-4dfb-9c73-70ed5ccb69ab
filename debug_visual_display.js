/**
 * Debug script for visual display issues
 * This script checks why the content is generated but not visible
 */

console.log('👁️ === VISUAL DISPLAY DEBUG START ===');

const debugVisualDisplay = () => {
  console.log('🔍 Debugging visual display...');
  
  // Step 1: Check Segment1 element in detail
  const segment1 = document.getElementById('Segment1');
  if (segment1) {
    console.log('✅ Segment1 element found');
    console.log('📊 Segment1 innerHTML length:', segment1.innerHTML.length);
    console.log('📊 Segment1 innerHTML preview:', segment1.innerHTML.substring(0, 200) + '...');
    
    // Check computed styles
    const styles = getComputedStyle(segment1);
    console.log('📊 Segment1 computed styles:');
    console.log('  - display:', styles.display);
    console.log('  - visibility:', styles.visibility);
    console.log('  - opacity:', styles.opacity);
    console.log('  - position:', styles.position);
    console.log('  - z-index:', styles.zIndex);
    console.log('  - width:', styles.width);
    console.log('  - height:', styles.height);
    console.log('  - overflow:', styles.overflow);
    console.log('  - flex-direction:', styles.flexDirection);
    
    // Check if it contains the expected content
    const hasActiveGoalCard = segment1.innerHTML.includes('Fit in deiner Umgebung');
    const hasActiveStatus = segment1.innerHTML.includes('Gerade aktiv');
    const hasProgressText = segment1.innerHTML.includes('von 4 Challenges');
    const hasNoGoalMessage = segment1.innerHTML.includes('Kein aktives Gesundheitsziel');
    
    console.log('📊 Content analysis:');
    console.log('  - Contains "Fit in deiner Umgebung":', hasActiveGoalCard);
    console.log('  - Contains "Gerade aktiv":', hasActiveStatus);
    console.log('  - Contains progress text:', hasProgressText);
    console.log('  - Contains "no goal" message:', hasNoGoalMessage);
    
    // Check for child elements
    const children = segment1.children;
    console.log('📊 Number of child elements:', children.length);
    for (let i = 0; i < children.length; i++) {
      const child = children[i];
      const childStyles = getComputedStyle(child);
      console.log(`  Child ${i}:`, child.tagName, 'display:', childStyles.display, 'visibility:', childStyles.visibility);
    }
    
    // Check for health goal cards specifically
    const healthGoalCards = segment1.querySelectorAll('.healthgoal-card, [class*="healthgoal"]');
    console.log('📊 Health goal cards found:', healthGoalCards.length);
    healthGoalCards.forEach((card, index) => {
      const cardStyles = getComputedStyle(card);
      console.log(`  Card ${index}:`, card.className, 'display:', cardStyles.display, 'visibility:', cardStyles.visibility);
      console.log(`    Card content preview:`, card.innerHTML.substring(0, 100) + '...');
    });
    
  } else {
    console.log('❌ Segment1 element not found');
  }
  
  // Step 2: Check the segmented control container
  const container = document.getElementById('segmentedControlContainer');
  if (container) {
    console.log('✅ Segmented control container found');
    const containerStyles = getComputedStyle(container);
    console.log('📊 Container styles:');
    console.log('  - display:', containerStyles.display);
    console.log('  - visibility:', containerStyles.visibility);
    console.log('  - opacity:', containerStyles.opacity);
    
    // Check all tab content areas
    const tabContents = container.querySelectorAll('.tabcontent');
    console.log('📊 Tab content areas:', tabContents.length);
    tabContents.forEach((tab, index) => {
      const tabStyles = getComputedStyle(tab);
      console.log(`  Tab ${index} (${tab.id}):`, 'display:', tabStyles.display, 'visibility:', tabStyles.visibility);
    });
    
  } else {
    console.log('❌ Segmented control container not found');
  }
  
  // Step 3: Check for overlapping elements
  console.log('🔍 Checking for overlapping elements...');
  const allElements = document.querySelectorAll('*');
  let overlappingElements = [];
  
  if (segment1) {
    const segment1Rect = segment1.getBoundingClientRect();
    console.log('📊 Segment1 position:', segment1Rect);
    
    allElements.forEach(el => {
      if (el !== segment1 && el.contains && !segment1.contains(el)) {
        const elRect = el.getBoundingClientRect();
        const styles = getComputedStyle(el);
        
        // Check if element overlaps and has higher z-index
        if (elRect.left < segment1Rect.right && 
            elRect.right > segment1Rect.left && 
            elRect.top < segment1Rect.bottom && 
            elRect.bottom > segment1Rect.top &&
            parseInt(styles.zIndex) > parseInt(getComputedStyle(segment1).zIndex)) {
          overlappingElements.push({
            element: el,
            zIndex: styles.zIndex,
            className: el.className,
            id: el.id
          });
        }
      }
    });
    
    if (overlappingElements.length > 0) {
      console.log('⚠️ Found overlapping elements with higher z-index:');
      overlappingElements.forEach((item, index) => {
        console.log(`  ${index}: ${item.element.tagName} (${item.className}) z-index: ${item.zIndex}`);
      });
    } else {
      console.log('✅ No overlapping elements found');
    }
  }
  
  // Step 4: Force visibility
  console.log('🔧 Attempting to force visibility...');
  if (segment1) {
    segment1.style.display = 'flex';
    segment1.style.flexDirection = 'column';
    segment1.style.visibility = 'visible';
    segment1.style.opacity = '1';
    segment1.style.zIndex = '9999';
    segment1.style.position = 'relative';
    segment1.style.backgroundColor = 'rgba(255, 0, 0, 0.1)'; // Light red background for debugging
    
    console.log('✅ Applied force visibility styles');
    
    // Also force visibility on child elements
    const children = segment1.querySelectorAll('*');
    children.forEach(child => {
      child.style.visibility = 'visible';
      child.style.opacity = '1';
    });
    
    console.log(`✅ Applied visibility to ${children.length} child elements`);
  }
};

// Step 5: Check what the user actually sees
const checkUserView = () => {
  console.log('👀 Checking what user sees...');
  
  // Take a "screenshot" by analyzing visible elements
  const visibleElements = [];
  const allElements = document.querySelectorAll('*');
  
  allElements.forEach(el => {
    const rect = el.getBoundingClientRect();
    const styles = getComputedStyle(el);
    
    if (rect.width > 0 && rect.height > 0 && 
        styles.display !== 'none' && 
        styles.visibility !== 'hidden' && 
        styles.opacity !== '0') {
      
      if (el.textContent && el.textContent.trim().length > 0) {
        visibleElements.push({
          element: el.tagName,
          className: el.className,
          id: el.id,
          text: el.textContent.trim().substring(0, 50),
          position: {
            top: rect.top,
            left: rect.left,
            width: rect.width,
            height: rect.height
          }
        });
      }
    }
  });
  
  console.log('👀 Visible elements with text:');
  visibleElements
    .filter(el => el.text.includes('Gesundheitsziel') || el.text.includes('Fit in') || el.text.includes('Challenges'))
    .forEach(el => {
      console.log(`  ${el.element} (${el.className}): "${el.text}"`);
    });
};

// Run the debug
debugVisualDisplay();
setTimeout(checkUserView, 1000);

// Make functions available globally
window.visualDebug = {
  debug: debugVisualDisplay,
  checkView: checkUserView
};

console.log('👁️ === VISUAL DISPLAY DEBUG END ===');
console.log('💡 Available functions:');
console.log('  - window.visualDebug.debug() - Run visual debug');
console.log('  - window.visualDebug.checkView() - Check what user sees');
