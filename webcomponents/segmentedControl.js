import { html } from "lit-html";
import "./segmentedControl.css";

export function createSegmentedControl(segments) {
  // Store segments for dynamic content updates
  const segmentData = segments;

  // Function to update segment content dynamically
  const updateSegmentContent = (segmentId) => {
    const segment = segmentData.find(s => s.id === segmentId);
    if (segment && typeof segment.content === 'function') {
      console.log(`🔄 Updating dynamic content for segment: ${segmentId}`);
      const contentElement = document.getElementById(segmentId);
      if (contentElement) {
        console.log(`📍 Found content element for ${segmentId}, updating...`);
        // Import render function for dynamic updates
        import('lit-html').then(({ render }) => {
          const newContent = segment.content();
          console.log(`✅ Generated new content for ${segmentId}:`, newContent);
          render(newContent, contentElement);
          console.log(`🎯 Content updated for ${segmentId}`);
        }).catch(error => {
          console.error(`❌ Error updating content for ${segmentId}:`, error);
        });
      } else {
        console.warn(`⚠️ Content element not found for segment: ${segmentId}`);
      }
    } else {
      console.log(`📄 Segment ${segmentId} has static content or not found`);
    }
  };

  // Function to get initial content - handles both static content and dynamic functions
  const getSegmentContent = (segment) => {
    if (typeof segment.content === 'function') {
      console.log(`🔄 Calling dynamic content function for segment: ${segment.id}`);
      return segment.content();
    } else {
      console.log(`📄 Using static content for segment: ${segment.id}`);
      return segment.content;
    }
  };

  const segmentedControlTemplate = html`
    <div class="tab">
      ${segments.map(
        (segment) => html`
          <button class="tablinks" @click=${(e) => openSegment(e, segment.id)}>
            ${segment.title}
          </button>
        `
      )}
    </div>
    ${segments.map(
      (segment) => html`
        <div id="${segment.id}" class="tabcontent">${getSegmentContent(segment)}</div>
      `
    )}
  `;

  function openSegment(evt, segmentId) {
    console.log(`🔄 Opening segment: ${segmentId}`);

    const tabcontent = document.getElementsByClassName("tabcontent");
    for (let i = 0; i < tabcontent.length; i++) {
      tabcontent[i].style.display = "none";
    }
    const tablinks = document.getElementsByClassName("tablinks");
    for (let i = 0; i < tablinks.length; i++) {
      tablinks[i].className = tablinks[i].className.replace(" active", "");
    }

    // Update dynamic content before showing
    updateSegmentContent(segmentId);

    document.getElementById(segmentId).style.display = "flex";
    evt.currentTarget.className += " active";
  }

  return {
    template: segmentedControlTemplate,
    initialize: () => {
      console.log("🚀 Initializing segmented control");
      const firstTab = document.querySelector(".tablinks");
      if (firstTab) {
        console.log("✅ First Tab found, clicking");
        firstTab.click();
        firstTab.classList.add("active");

        // Also update dynamic content for the first segment with multiple attempts
        const firstSegment = segmentData[0];
        if (firstSegment) {
          console.log(`🔄 Scheduling dynamic content update for first segment: ${firstSegment.id}`);

          // Try multiple times with increasing delays to ensure DOM is ready
          setTimeout(() => updateSegmentContent(firstSegment.id), 50);
          setTimeout(() => updateSegmentContent(firstSegment.id), 150);
          setTimeout(() => updateSegmentContent(firstSegment.id), 300);
        }
      } else {
        console.log("❌ No .tablinks element found");
      }
    },
    // Expose updateSegmentContent for external use
    updateContent: updateSegmentContent,
  };
}

console.log("Segmented control module loaded");
