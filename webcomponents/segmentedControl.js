import { html, render } from "lit-html";
import "./segmentedControl.css";

export function createSegmentedControl(segments) {
  // Store segments for dynamic content updates
  const segmentData = segments;

  // Function to update segment content dynamically
  const updateSegmentContent = (segmentId) => {
    console.log(`🔄 updateSegmentContent called for: ${segmentId}`);
    const segment = segmentData.find(s => s.id === segmentId);
    console.log(`📋 Found segment:`, segment);

    if (segment && typeof segment.content === 'function') {
      console.log(`🔄 Updating dynamic content for segment: ${segmentId}`);
      const contentElement = document.getElementById(segmentId);
      console.log(`📍 Content element for ${segmentId}:`, contentElement);

      if (contentElement) {
        console.log(`📍 Found content element for ${segmentId}, updating...`);

        try {
          // Call the content function directly first
          console.log(`🎯 Calling content function for ${segmentId}...`);
          const newContent = segment.content();
          console.log(`✅ Generated new content for ${segmentId}:`, newContent);

          // Use direct render with imported function
          try {
            console.log(`🎨 Rendering new content for ${segmentId}...`);
            render(newContent, contentElement);
            console.log(`🎯 Content updated for ${segmentId}`);

            // Force display update
            contentElement.style.display = 'flex';
            contentElement.style.flexDirection = 'column';
            console.log(`👁️ Forced display update for ${segmentId}`);

          } catch (error) {
            console.error(`❌ Error rendering content for ${segmentId}:`, error);
            console.error(`❌ Error details:`, error.stack);

            // Fallback: Set innerHTML directly
            console.log(`🔄 Trying fallback innerHTML method for ${segmentId}...`);
            try {
              // Clear content first
              contentElement.innerHTML = '';

              // Try to render as string
              if (newContent && typeof newContent === 'object' && newContent.strings) {
                // This is a lit-html template, try to extract the strings
                contentElement.innerHTML = newContent.strings.join('<!-- template part -->');
              } else {
                contentElement.innerHTML = String(newContent);
              }
              console.log(`✅ Fallback innerHTML method worked for ${segmentId}`);
            } catch (fallbackError) {
              console.error(`❌ Fallback method also failed for ${segmentId}:`, fallbackError);

              // Last resort: Show error message
              contentElement.innerHTML = `<div class="error">Error loading content for ${segmentId}</div>`;
            }
          }

        } catch (error) {
          console.error(`❌ Error calling content function for ${segmentId}:`, error);
        }
      } else {
        console.warn(`⚠️ Content element not found for segment: ${segmentId}`);

        // Try to find it with a different approach
        const allElements = document.querySelectorAll(`[id="${segmentId}"]`);
        console.log(`🔍 Alternative search found ${allElements.length} elements with id ${segmentId}`);
        allElements.forEach((el, index) => {
          console.log(`Element ${index}:`, el);
        });
      }
    } else {
      console.log(`📄 Segment ${segmentId} has static content or not found`);
      console.log(`📄 Segment content type:`, typeof segment?.content);
    }
  };

  // Function to get initial content - handles both static content and dynamic functions
  const getSegmentContent = (segment) => {
    if (typeof segment.content === 'function') {
      console.log(`🔄 Calling dynamic content function for segment: ${segment.id}`);
      return segment.content();
    } else {
      console.log(`📄 Using static content for segment: ${segment.id}`);
      return segment.content;
    }
  };

  const segmentedControlTemplate = html`
    <div class="tab">
      ${segments.map(
        (segment) => html`
          <button class="tablinks" @click=${(e) => openSegment(e, segment.id)}>
            ${segment.title}
          </button>
        `
      )}
    </div>
    ${segments.map(
      (segment) => html`
        <div id="${segment.id}" class="tabcontent">${getSegmentContent(segment)}</div>
      `
    )}
  `;

  function openSegment(evt, segmentId) {
    console.log(`🔄 Opening segment: ${segmentId}`);

    const tabcontent = document.getElementsByClassName("tabcontent");
    for (let i = 0; i < tabcontent.length; i++) {
      tabcontent[i].style.display = "none";
    }
    const tablinks = document.getElementsByClassName("tablinks");
    for (let i = 0; i < tablinks.length; i++) {
      tablinks[i].className = tablinks[i].className.replace(" active", "");
    }

    // Update dynamic content before showing
    updateSegmentContent(segmentId);

    document.getElementById(segmentId).style.display = "flex";
    evt.currentTarget.className += " active";
  }

  return {
    template: segmentedControlTemplate,
    initialize: () => {
      console.log("🚀 Initializing segmented control");
      console.log("📊 Available segments:", segmentData.map(s => ({ id: s.id, contentType: typeof s.content })));

      const firstTab = document.querySelector(".tablinks");
      if (firstTab) {
        console.log("✅ First Tab found, clicking");
        firstTab.click();
        firstTab.classList.add("active");

        // Also update dynamic content for the first segment with multiple attempts
        const firstSegment = segmentData[0];
        if (firstSegment) {
          console.log(`🔄 Scheduling dynamic content update for first segment: ${firstSegment.id}`);

          // Try multiple times with increasing delays to ensure DOM is ready
          setTimeout(() => {
            console.log(`🕐 Attempt 1: Updating content for ${firstSegment.id}`);
            updateSegmentContent(firstSegment.id);
          }, 50);

          setTimeout(() => {
            console.log(`🕐 Attempt 2: Updating content for ${firstSegment.id}`);
            updateSegmentContent(firstSegment.id);
          }, 150);

          setTimeout(() => {
            console.log(`🕐 Attempt 3: Updating content for ${firstSegment.id}`);
            updateSegmentContent(firstSegment.id);
          }, 300);

          setTimeout(() => {
            console.log(`🕐 Attempt 4: Updating content for ${firstSegment.id}`);
            updateSegmentContent(firstSegment.id);
          }, 500);
        }
      } else {
        console.log("❌ No .tablinks element found");

        // Try to find tabs with alternative approach
        setTimeout(() => {
          const allTabs = document.querySelectorAll("button");
          console.log(`🔍 Found ${allTabs.length} buttons on page`);
          const tabButtons = Array.from(allTabs).filter(btn => btn.textContent.includes("Ziele"));
          console.log(`🔍 Found ${tabButtons.length} potential tab buttons`);

          if (tabButtons.length > 0) {
            console.log("✅ Found alternative tab button, clicking");
            tabButtons[0].click();
            tabButtons[0].classList.add("active");
          }
        }, 200);
      }
    },
    // Expose updateSegmentContent for external use
    updateContent: updateSegmentContent,
  };
}

console.log("Segmented control module loaded");
