import { html } from "lit-html";
import "./healthgoalOverview_Segments.css";
import { healthgoalCardActive } from "../healthgoalCardActive.js";
import { healthgoalCardInactive } from "../healthgoalCardInactive.js";
import { infoCardSlider } from "../infoCardSlider.js";
import { sectionSubtitle } from "../sectionSubtitle.js";
import imgKomootHG from "../../img/healthgoals/komoot_hg.jpg";
import imgPeople from "../../img/people_hugging.jpg";
import iconTagSport from "../../svg/icons/icon_tag_sport.svg";
import iconMoneypig from "../../svg/icons/icon_moneypig.svg";
import iconBell from "../../svg/icons/icon_bell.svg";
import iconTimelater from "../../svg/icons/icon_timelater.svg";
import imgCyberfitness from "../../img/cyberfitness.jpg";
import { createChips } from "../chip_filterChip.js";
import { createCardFilter, addCategoryAttributes } from "../../src/utils/cardFilter.js";

/* Filter Chips */
const chips = createChips("Gemischt Ernährung Psyche Schlaf Bewegung");

// Kategorien für die Karten
const categories = ["Bewegung", "Bewegung", "Schlaf"];

// Erstelle Filter-Funktion
const filterCards = createCardFilter(
  '.healthgoal-cards', 
  '.healthgoal-card'
);

export const healthgoalOverview_segment1Content = html`
  <div class="tabpadding">
    <div class="screen-centered content-padding">
      <h2 class="tabheadline">Dein aktives Ziel</h2>
      <div class="healthgoal-cards">
        <a data-navigate="/healthgoals-overview/hg-fitUmgebung">
          ${healthgoalCardActive({
            cardImage: imgKomootHG,
            tagCategory: "Bewegung",
            tagIcon: iconTagSport,
            tagColor: "--tag-sport",
            tagTextColor: "--tag-text",
            healthgoalTitle: "Fit in deiner Umgebung",
            healthgoalCoop: "In Kooperation komoot",
            pillText: "Gerade aktiv",
            pillColor: "--accent-blue",
            goalinfoIcon1: iconMoneypig,
            goalinfoText1: "1500 Bonuspunkte",
            goalinfoIcon2: iconTimelater,
            goalinfoText2: "max. 90 Tage",
            completedChallenges: 3,
            totalChallenges: 4,
          })}
        </a>
      </div>
    </div>
    <div class="content-padding firstcard-no-top-padding">
      <!-- Info Box Push Notification -->
      <div class="standard-container">
        ${infoCardSlider({
          icoPath: iconBell,
          title: "Du möchtest nichts vergessen?",
          text: "Stelle hier meine Erinnerungen an Dich ein",
          linkText: "Einstellungen",
        })}
      </div>
      ${sectionSubtitle("Alle Gesundheitsziele")}
      <!-- Sub Title here -->
      <div class="standard-container">
        ${chips.template}
        <!-- Filter Chips here -->
      </div>
      <div class="healthgoal-cards">
        ${healthgoalCardInactive({
          cardImage: imgCyberfitness,
          tagCategory: "Bewegung",
          tagIcon: iconTagSport,
          tagColor: "--tag-sport",
          tagTextColor: "--tag-text",
          healthgoalTitle: "Mehr Bewegung im Alltag",
          healthgoalCoop: "In Kooperation mit Komoot",
          pillText: "Noch 7 Tage verfügbar",
          pillColor: "--primary-brand",
          goalinfoIcon1: iconMoneypig,
          goalinfoText1: "1500 Bonuspunkte",
          goalinfoIcon2: iconTimelater,
          goalinfoText2: "max. 90 Tage",
        })}
        ${healthgoalCardInactive({
          cardImage: imgPeople,
          tagCategory: "Schlaf",
          tagIcon: iconTagSport,
          tagColor: "--tag-sleep",
          tagTextColor: "--tag-text",
          healthgoalTitle: "Gesunder Schlaf",
          healthgoalCoop: "",
          pillText: "",
          pillColor: "--primary-brand",
          goalinfoIcon1: iconMoneypig,
          goalinfoText1: "1500 Bonuspunkte",
          goalinfoIcon2: iconTimelater,
          goalinfoText2: "max. 90 Tage",
        })}
      </div>
    </div>
  </div>
`;

// Initialisiere die Chips nach dem Rendern
setTimeout(() => {
  chips.initialize();
  
  // Füge Kategorie-Attribute zu den Karten hinzu
  addCategoryAttributes(
    '.healthgoal-cards', 
    '.healthgoal-card', 
    categories
  );
}, 0);
