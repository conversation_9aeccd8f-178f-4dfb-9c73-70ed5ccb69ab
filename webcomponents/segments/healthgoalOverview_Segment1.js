import { html } from "lit-html";
import "./healthgoalOverview_Segments.css";
import { healthgoalCardActive } from "../healthgoalCardActive.js";
import { healthgoalCardInactive } from "../healthgoalCardInactive.js";
import { infoCardSlider } from "../infoCardSlider.js";
import { sectionSubtitle } from "../sectionSubtitle.js";
import iconBell from "../../svg/icons/icon_bell.svg";
import { createChips } from "../chip_filterChip.js";
import { addCategoryAttributes } from "../../src/utils/cardFilter.js";
import { getActiveHealthGoal, getHealthGoalCardProps, getAllHealthGoalsWithProgress, verifyDataFlowConnections } from "../../src/utils/healthGoalUtils.js";

/* Filter Chips */
const chips = createChips("Gemischt Ernährung Psyche Schlaf Bewegung");

// Dynamic categories based on health goals
const getDynamicCategories = () => {
  try {
    const allHealthGoals = getAllHealthGoalsWithProgress();
    return allHealthGoals.map(goal => goal.tagCategory).filter(Boolean);
  } catch (error) {
    console.error('Error getting dynamic categories:', error);
    // Fallback to default categories
    return ["Bewegung", "Schlaf", "Ernährung", "Psyche"];
  }
};

// Filter function available if needed
// const filterCards = createCardFilter('.healthgoal-cards', '.healthgoal-card');

// Function to generate dynamic content
const generateDynamicContent = () => {
  try {
    console.log('🔄 generateDynamicContent() called - checking for active health goal');

    // Get active health goal with dynamic progress data
    const activeHealthGoal = getActiveHealthGoal();
    console.log('📊 Active health goal result:', activeHealthGoal);

    // If no active health goal, show placeholder or empty state
    if (!activeHealthGoal) {
      return html`
        <div class="tabpadding">
          <div class="screen-centered content-padding">
            <h2 class="tabheadline">Dein aktives Ziel</h2>
            <div class="healthgoal-cards">
              <p class="caption">Kein aktives Gesundheitsziel gefunden. Wähle ein Ziel aus der Liste unten.</p>
            </div>
          </div>
        </div>
      `;
    }

    // Get card properties for active health goal
    const cardProps = getHealthGoalCardProps(activeHealthGoal.id, true);

    if (!cardProps) {
      console.error('Could not get card properties for active health goal:', activeHealthGoal.id);
      return html`
        <div class="tabpadding">
          <div class="screen-centered content-padding">
            <h2 class="tabheadline">Dein aktives Ziel</h2>
            <div class="healthgoal-cards">
              <p class="caption">Fehler beim Laden des aktiven Gesundheitsziels.</p>
            </div>
          </div>
        </div>
      `;
    }

    return html`
      <div class="tabpadding">
        <div class="screen-centered content-padding">
          <h2 class="tabheadline">Dein aktives Ziel</h2>
          <div class="healthgoal-cards">
            <a data-navigate="${activeHealthGoal.link || '/healthgoals-overview/hg-fitUmgebung'}">
              ${healthgoalCardActive(cardProps)}
            </a>
          </div>
        </div>
      </div>
    `;
  } catch (error) {
    console.error('Error generating dynamic content:', error);
    return html`
      <div class="tabpadding">
        <div class="screen-centered content-padding">
          <h2 class="tabheadline">Dein aktives Ziel</h2>
          <div class="healthgoal-cards">
            <p class="caption">Fehler beim Laden der Gesundheitsziele.</p>
          </div>
        </div>
      </div>
    `;
  }
};

// Function to generate inactive health goals dynamically
const generateInactiveHealthGoals = () => {
  try {
    const allHealthGoals = getAllHealthGoalsWithProgress();
    const inactiveHealthGoals = allHealthGoals.filter(goal => !goal.active);

    return inactiveHealthGoals.map(goal => {
      const cardProps = getHealthGoalCardProps(goal.id, false);
      if (!cardProps) {
        console.error('Could not get card properties for health goal:', goal.id);
        return html`<p class="caption">Fehler beim Laden von ${goal.healthgoalTitle || goal.id}</p>`;
      }
      return healthgoalCardInactive(cardProps);
    });
  } catch (error) {
    console.error('Error generating inactive health goals:', error);
    return html`<p class="caption">Fehler beim Laden der Gesundheitsziele.</p>`;
  }
};

// Export as a function that returns the template (dynamic evaluation)
export const healthgoalOverview_segment1Content = () => {
  console.log('🔄 healthgoalOverview_segment1Content() called - generating fresh template');

  // Run data flow verification for debugging
  verifyDataFlowConnections('fitUmgebung');

  return html`
    ${generateDynamicContent()}
      <div class="content-padding firstcard-no-top-padding">
        <!-- Info Box Push Notification -->
        <div class="standard-container">
          ${infoCardSlider({
            icoPath: iconBell,
            title: "Du möchtest nichts vergessen?",
            text: "Stelle hier meine Erinnerungen an Dich ein",
            linkText: "Einstellungen",
          })}
        </div>
        ${sectionSubtitle("Alle Gesundheitsziele")}
        <!-- Sub Title here -->
        <div class="standard-container">
          ${chips.template}
          <!-- Filter Chips here -->
        </div>
        <div class="healthgoal-cards">
          ${generateInactiveHealthGoals()}
        </div>
      </div>
    </div>
  `;
};

// Initialisiere die Chips nach dem Rendern
setTimeout(() => {
  chips.initialize();

  // Füge Kategorie-Attribute zu den Karten hinzu mit dynamischen Kategorien
  const dynamicCategories = getDynamicCategories();
  addCategoryAttributes(
    '.healthgoal-cards',
    '.healthgoal-card',
    dynamicCategories
  );
}, 0);
