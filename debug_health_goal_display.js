/**
 * Debug script for health goal display issue
 * Run this in the browser console to debug the issue
 */

console.log('🐛 === HEALTH GOAL DISPLAY DEBUG START ===');

// Step 1: Check appStorage state
console.log('📊 Step 1: Checking appStorage state');
console.log('appStorage._data:', appStorage._data);
console.log('appStorage.activeHealthGoals:', appStorage.activeHealthGoals);
console.log('appStorage._data.activeHealthGoals:', appStorage._data.activeHealthGoals);

// Step 2: Activate health goal if not active
console.log('📊 Step 2: Ensuring health goal is active');
if (!appStorage.activeHealthGoals.fitUmgebung) {
  console.log('⚠️ Health goal not active, activating...');
  appStorage.activateHealthGoal('fitUmgebung');
} else {
  console.log('✅ Health goal already active');
}

// Step 3: Test getActiveHealthGoal function
console.log('📊 Step 3: Testing getActiveHealthGoal function');
import('./src/utils/healthGoalUtils.js').then(({ getActiveHealthGoal }) => {
  const activeGoal = getActiveHealthGoal();
  console.log('Active goal result:', activeGoal);
  
  if (activeGoal) {
    console.log('✅ getActiveHealthGoal working correctly');
  } else {
    console.log('❌ getActiveHealthGoal not working');
  }
});

// Step 4: Test template function
console.log('📊 Step 4: Testing template function');
import('./webcomponents/segments/healthgoalOverview_Segment1.js').then(({ healthgoalOverview_segment1Content }) => {
  console.log('Template function type:', typeof healthgoalOverview_segment1Content);
  
  if (typeof healthgoalOverview_segment1Content === 'function') {
    console.log('✅ Template function is available');
    
    // Call the template function
    const templateResult = healthgoalOverview_segment1Content();
    console.log('Template result:', templateResult);
    
    if (templateResult) {
      console.log('✅ Template function returns content');
    } else {
      console.log('❌ Template function returns empty content');
    }
  } else {
    console.log('❌ Template function not available');
  }
});

// Step 5: Check DOM elements
console.log('📊 Step 5: Checking DOM elements');
setTimeout(() => {
  const segmentedControlContainer = document.getElementById('segmentedControlContainer');
  console.log('Segmented control container:', segmentedControlContainer);
  
  if (segmentedControlContainer) {
    const tabContent = segmentedControlContainer.querySelector('.tabcontent');
    console.log('Tab content element:', tabContent);
    
    if (tabContent) {
      console.log('Tab content innerHTML length:', tabContent.innerHTML.length);
      console.log('Tab content display style:', getComputedStyle(tabContent).display);
      console.log('Tab content flex direction:', getComputedStyle(tabContent).flexDirection);
    }
    
    const tabLinks = segmentedControlContainer.querySelectorAll('.tablinks');
    console.log('Tab links found:', tabLinks.length);
    
    tabLinks.forEach((link, index) => {
      console.log(`Tab link ${index}:`, link.textContent, 'active:', link.classList.contains('active'));
    });
  }
}, 1000);

// Step 6: Test segmented control initialization
console.log('📊 Step 6: Testing segmented control initialization');
setTimeout(() => {
  if (window.segmentedControl && typeof window.segmentedControl.initialize === 'function') {
    console.log('✅ Segmented control available, initializing...');
    window.segmentedControl.initialize();
  } else {
    console.log('❌ Segmented control not available');
    console.log('Available on window:', Object.keys(window).filter(key => key.includes('segment')));
  }
}, 1500);

// Step 7: Manual template update test
console.log('📊 Step 7: Manual template update test');
setTimeout(() => {
  const segment1 = document.getElementById('Segment1');
  if (segment1) {
    console.log('✅ Segment1 element found');
    
    // Try to manually update content
    import('./webcomponents/segments/healthgoalOverview_Segment1.js').then(({ healthgoalOverview_segment1Content }) => {
      import('lit-html').then(({ render }) => {
        console.log('🔄 Manually updating Segment1 content...');
        const newContent = healthgoalOverview_segment1Content();
        render(newContent, segment1);
        console.log('✅ Manual content update completed');
      });
    });
  } else {
    console.log('❌ Segment1 element not found');
  }
}, 2000);

console.log('🐛 === HEALTH GOAL DISPLAY DEBUG END ===');
console.log('💡 Check the console output above for debug information');
console.log('💡 If the issue persists, the problem is likely in the segmented control initialization');
