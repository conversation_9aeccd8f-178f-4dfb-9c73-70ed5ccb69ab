# Dynamic Health Goals and Challenges Tracking System

## Overview

The Dynamic Health Goals and Challenges Tracking System is a comprehensive solution for managing health goal progress, challenge completion, and training tracking in the AOK NAVIDA web application. This system implements a hierarchical structure with automatic completion rules and real-time progress updates.

## System Architecture

### Hierarchical Structure

```
Health Goal (1 active at a time)
├── Challenge 1 (multiple per health goal, 1 active at a time)
│   ├── Training 1 (multiple per challenge, sequential completion)
│   ├── Training 2
│   └── Training N
├── Challenge 2
└── Challenge N
```

### Core Components

1. **Configuration System** (`src/config/healthGoalsConfig.js`)
2. **Data Storage** (`utils.js` - appStorage extensions)
3. **Utility Functions** (`src/utils/healthGoalUtils.js`)
4. **Challenge Completion Logic** (`src/utils/challengeCompletionUtils.js`)
5. **UI Integration** (Dynamic data in components)

## Data Structure

### Health Goal Progress Structure

```javascript
healthGoalProgress: {
  fitUmgebung: {
    completedChallenges: 0,        // Number of completed challenges
    totalChallenges: 4,            // Total challenges for this health goal
    challengeCompletions: {        // Individual challenge tracking
      "lockereWanderung": {
        completed: false,          // Challenge completion status
        completedTrainings: 0,     // Number of completed trainings
        totalTrainings: 2          // Total trainings required
      }
    }
  }
}
```

### Health Goal Configuration

```javascript
fitUmgebung: {
  id: "fitUmgebung",
  cardImage: imgKomootPortrait,
  cardImageActive: imgKomootHG,
  tagCategory: "Bewegung",
  tagIcon: iconTagSport,
  tagColor: "--tag-sport",
  tagTextColor: "--tag-text",
  healthgoalTitle: "Fit in deiner Umgebung",
  healthgoalCoop: "In Kooperation mit Komoot",
  pillText: "Gerade aktiv",
  pillColor: "--accent-blue",
  goalinfoIcon1: iconMoneypig,
  goalinfoText1: "1500 Bonuspunkte",
  goalinfoIcon2: iconTimelater,
  goalinfoText2: "max. 90 Tage",
  totalChallenges: 4,
  link: "/healthgoals-overview/hg-fitUmgebung",
  active: false
}
```

## Completion Rules

### 80% Completion Rule

A challenge is automatically marked as completed when ≥80% of its required trainings are completed:

- **Example**: Challenge requires 2 trainings
  - 1 training completed = 50% (not completed)
  - 2 trainings completed = 100% (completed ✓)

- **Example**: Challenge requires 5 trainings
  - 3 trainings completed = 60% (not completed)
  - 4 trainings completed = 80% (completed ✓)

### Health Goal Completion

A health goal's progress is tracked by the number of completed challenges:
- `completedChallenges` / `totalChallenges` = progress percentage
- Display format: "X von Y Challenges geschafft"

## Key Functions

### Health Goal Management

```javascript
// Initialize the system
initializeHealthGoalSystem()

// Get active health goal with progress
const activeGoal = getActiveHealthGoal()

// Get health goal with progress data
const goalWithProgress = getHealthGoalWithProgress('fitUmgebung')

// Activate a health goal
activateHealthGoal('fitUmgebung')
```

### Challenge Management

```javascript
// Record training completion
const result = recordTrainingCompletion('lockereWanderung', 1)

// Get challenge status
const status = getChallengeStatus('lockereWanderung')

// Start a challenge
const startResult = startChallenge('lockereWanderung')
```

### Progress Tracking

```javascript
// Update health goal progress
updateHealthGoalProgress('fitUmgebung', 'lockereWanderung', 2, 2)

// Get completion percentage
const percentage = getHealthGoalCompletionPercentage('fitUmgebung')

// Get completion summary
const summary = getHealthGoalCompletionSummary('fitUmgebung')
```

### Reset and Validation

```javascript
// Reset all progress
resetHealthGoalProgress(true)

// Reset specific health goal
resetSpecificHealthGoal('fitUmgebung', true)

// Validate system integrity
const validation = validateHealthGoalSystem()
```

## Data Flow

### 1. System Initialization

```
App Start → appStorage.init() → initializeHealthGoalSystem() → 
validateHealthGoalSystem() → Ready for Use
```

### 2. Training Completion Flow

```
User completes training → recordTrainingCompletion() → 
updateChallengeTrainingProgress() → Check 80% rule → 
markChallengeCompleted() (if ≥80%) → Update UI
```

### 3. Challenge Progress Flow

```
Challenge started → startChallenge() → Initialize tracking →
Training completions → Automatic challenge completion →
Update health goal progress → UI updates
```

### 4. Reset Flow

```
Home button clicked → resetHealthGoalProgress() → 
appStorage.reset() → initializeHealthGoalSystem() → 
validateHealthGoalSystem() → Fresh state
```

## UI Integration

### Dynamic Health Goal Cards

The system replaces hardcoded values with dynamic data:

**Before:**
```javascript
completedChallenges: 3,  // Hardcoded
totalChallenges: 4,      // Hardcoded
```

**After:**
```javascript
const cardProps = getHealthGoalCardProps('fitUmgebung', true)
// Uses real-time data from appStorage
```

### Progress Display

Progress is displayed as: "X von Y Challenges geschafft"
- X = `completedChallenges` from appStorage
- Y = `totalChallenges` from configuration

## Configuration Management

### Adding New Health Goals

1. Add configuration to `healthGoalsConfig.js`
2. Set `totalChallenges` value
3. Add to `challengeConfig` in `challengeCompletionUtils.js`
4. System automatically initializes progress tracking

### Adding New Challenges

1. Add to `challengeConfig` mapping:
```javascript
'newChallenge': {
  healthGoalId: 'fitUmgebung',
  totalTrainings: 3,
  displayName: 'New Challenge'
}
```

2. Implement challenge page with `recordTrainingCompletion()`
3. System handles progress tracking automatically

## Error Handling

The system includes comprehensive error handling:

- **Graceful Fallbacks**: Missing data shows user-friendly messages
- **Validation**: System integrity checks on initialization and reset
- **Logging**: Detailed console logging for debugging
- **Recovery**: Automatic fallback to default values when needed

## Testing and Validation

### System Validation

```javascript
const validation = validateHealthGoalSystem()
console.log(validation.valid)     // true/false
console.log(validation.issues)    // Array of critical issues
console.log(validation.warnings)  // Array of warnings
```

### Manual Testing Steps

1. **Reset System**: Click home button, verify clean state
2. **Activate Goal**: Complete niveau poll, verify goal activation
3. **Start Challenge**: Connect tracker, verify challenge starts
4. **Complete Training**: Record training, verify progress updates
5. **Complete Challenge**: Reach 80% threshold, verify auto-completion
6. **UI Updates**: Verify real-time progress display updates

## Migration Notes

### Backward Compatibility

The system maintains compatibility with the existing challenge system:
- Old `appStorage.challenges` structure still works
- New system runs alongside old system
- Gradual migration possible

### Data Migration

No manual data migration required:
- System initializes fresh progress tracking
- Existing data preserved in old structure
- New features work immediately

## Performance Considerations

- **Lazy Loading**: Health goal data loaded on demand
- **Caching**: Configuration cached after first load
- **Minimal Storage**: Efficient data structure design
- **Real-time Updates**: Immediate UI updates without full re-render

## Future Enhancements

### Planned Features

1. **Multiple Active Goals**: Support for multiple concurrent health goals
2. **Custom Completion Rules**: Configurable completion percentages
3. **Progress Analytics**: Detailed progress tracking and statistics
4. **Notification System**: Progress milestone notifications
5. **Export/Import**: Progress data backup and restore

### Extension Points

- **Custom Validators**: Add custom validation rules
- **Progress Hooks**: Custom callbacks on progress events
- **UI Themes**: Customizable progress display themes
- **Data Adapters**: Support for external data sources

## Troubleshooting

### Common Issues

1. **Progress Not Updating**: Check `validateHealthGoalSystem()`
2. **Reset Not Working**: Verify home button event handling
3. **Missing Data**: Check configuration loading
4. **UI Not Refreshing**: Verify dynamic content generation

### Debug Commands

```javascript
// Check system status
validateHealthGoalSystem()

// View current progress
appStorage.getHealthGoalProgress('fitUmgebung')

// View all health goals
getAllHealthGoalsWithProgress()

// Reset and reinitialize
resetHealthGoalProgress(true)
```

## Implementation Examples

### Example 1: Adding a New Challenge

```javascript
// 1. Add to challengeConfig in challengeCompletionUtils.js
'newWalkingChallenge': {
  healthGoalId: 'fitUmgebung',
  totalTrainings: 3,
  displayName: 'New Walking Challenge'
}

// 2. In challenge page component
import { recordTrainingCompletion } from '../../utils/challengeCompletionUtils.js';

const handleTrainingComplete = () => {
  const result = recordTrainingCompletion('newWalkingChallenge');
  if (result.success && result.challengeCompleted) {
    console.log('🎉 Challenge completed!');
    // Show completion dialog or animation
  }
};
```

### Example 2: Custom Health Goal Configuration

```javascript
// Add to healthGoalsConfig.js
customHealthGoal: {
  id: "customHealthGoal",
  cardImage: imgCustom,
  tagCategory: "Custom",
  tagIcon: iconCustom,
  tagColor: "--tag-custom",
  tagTextColor: "--tag-text",
  healthgoalTitle: "Custom Health Goal",
  healthgoalCoop: "",
  pillText: "New Goal",
  pillColor: "--primary-brand",
  goalinfoIcon1: iconMoneypig,
  goalinfoText1: "2000 Bonuspunkte",
  goalinfoIcon2: iconTimelater,
  goalinfoText2: "max. 120 Tage",
  totalChallenges: 6,
  link: "/healthgoals-overview/hg-custom",
  active: false
}
```

### Example 3: Real-time Progress Updates

```javascript
// In component that displays progress
import { getHealthGoalWithProgress } from '../utils/healthGoalUtils.js';

const updateProgressDisplay = () => {
  const healthGoal = getHealthGoalWithProgress('fitUmgebung');
  const progressText = `${healthGoal.completedChallenges} von ${healthGoal.totalChallenges} Challenges geschafft`;

  // Update DOM element
  document.querySelector('.challenge-progress').textContent = progressText;

  // Update progress bar
  const percentage = (healthGoal.completedChallenges / healthGoal.totalChallenges) * 100;
  document.querySelector('.progress-bar').style.width = `${percentage}%`;
};

// Call after training completion
recordTrainingCompletion('lockereWanderung').then(() => {
  updateProgressDisplay();
});
```

## File Structure

```
src/
├── config/
│   └── healthGoalsConfig.js          # Centralized health goal configuration
├── utils/
│   ├── healthGoalUtils.js            # Health goal management utilities
│   └── challengeCompletionUtils.js   # Challenge completion logic
├── healthgoals/
│   └── challenges/
│       └── ch_lockereWanderung.js    # Updated with new completion system
└── trackerConnect.js                 # Updated challenge start logic

webcomponents/
└── segments/
    └── healthgoalOverview_Segment1.js # Dynamic health goal display

utils.js                              # Extended appStorage with progress tracking
index.js                              # System initialization and reset handling
dynamicHealthgoals.md                 # This documentation file
```

## API Reference

### healthGoalUtils.js

| Function | Parameters | Returns | Description |
|----------|------------|---------|-------------|
| `initializeHealthGoalSystem()` | None | void | Initialize health goal system |
| `getActiveHealthGoal()` | None | Object\|null | Get currently active health goal |
| `getHealthGoalWithProgress(goalId)` | goalId: string | Object\|null | Get health goal with progress data |
| `activateHealthGoal(goalId)` | goalId: string | boolean | Activate a health goal |
| `resetHealthGoalProgress(verbose)` | verbose: boolean | Object | Reset all progress |
| `validateHealthGoalSystem()` | None | Object | Validate system integrity |

### challengeCompletionUtils.js

| Function | Parameters | Returns | Description |
|----------|------------|---------|-------------|
| `recordTrainingCompletion(challengeName, count)` | challengeName: string, count: number | Object | Record training completion |
| `getChallengeStatus(challengeName)` | challengeName: string | Object | Get challenge status |
| `startChallenge(challengeName)` | challengeName: string | Object | Start a challenge |
| `getHealthGoalCompletionSummary(goalId)` | goalId: string | Object | Get completion summary |

### appStorage Extensions

| Method | Parameters | Returns | Description |
|--------|------------|---------|-------------|
| `getHealthGoalProgress(goalName)` | goalName: string | Object | Get progress data |
| `markChallengeCompleted(goalName, challengeName)` | goalName: string, challengeName: string | void | Mark challenge complete |
| `updateChallengeTrainingProgress(goalName, challengeName, completed, total)` | goalName: string, challengeName: string, completed: number, total: number | void | Update training progress |

## Best Practices

### 1. Error Handling
Always wrap health goal operations in try-catch blocks:

```javascript
try {
  const result = recordTrainingCompletion('challengeName');
  if (!result.success) {
    console.error('Training completion failed:', result.error);
    // Handle error appropriately
  }
} catch (error) {
  console.error('Unexpected error:', error);
  // Fallback behavior
}
```

### 2. Progress Updates
Update UI immediately after progress changes:

```javascript
const result = recordTrainingCompletion('lockereWanderung');
if (result.success) {
  // Update progress display immediately
  updateProgressDisplay();

  // Trigger animations
  if (result.challengeCompleted) {
    showCompletionAnimation();
  }
}
```

### 3. System Validation
Validate system state during development:

```javascript
// Add to development builds
if (process.env.NODE_ENV === 'development') {
  const validation = validateHealthGoalSystem();
  if (!validation.valid) {
    console.warn('Health Goal System Issues:', validation.issues);
  }
}
```

### 4. Configuration Management
Keep configuration centralized and typed:

```javascript
// Use consistent structure for all health goals
const healthGoalTemplate = {
  id: "",
  cardImage: null,
  tagCategory: "",
  tagIcon: null,
  tagColor: "",
  tagTextColor: "--tag-text",
  healthgoalTitle: "",
  healthgoalCoop: "",
  pillText: "",
  pillColor: "--primary-brand",
  goalinfoIcon1: null,
  goalinfoText1: "",
  goalinfoIcon2: null,
  goalinfoText2: "",
  totalChallenges: 0,
  link: "",
  active: false
};
```

---

*This documentation provides complete coverage of the Dynamic Health Goals and Challenges Tracking System. For questions or issues, refer to the troubleshooting section or examine the source code in the specified files.*
