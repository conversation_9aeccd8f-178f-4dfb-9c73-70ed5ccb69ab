import { html, render } from "lit-html";
import Navigo from "navigo";
import { router } from "./router.js";
import "./src/clock.js";
import { initDragScroll } from './helpers/dragScroll.js';
import { appStorage } from './utils.js';
import { initializeHealthGoalSystem, resetHealthGoalProgress, validateHealthGoalSystem } from './src/utils/healthGoalUtils.js';
import './src/utils/integrationTestRunner.js';

/* Parcel CSS Bugfix Hot Module Loading */
if (module.hot) {
  module.hot.accept();
}

const app = document.getElementById("app");

// Initialisiere den zentralen Speicher
appStorage.init();

// Initialisiere das Health Goal System
initializeHealthGoalSystem();

// Validiere das Health Goal System
const validation = validateHealthGoalSystem();
if (!validation.valid) {
  console.warn('⚠️ Health Goal System Validation Issues:', validation.issues);
}
if (validation.warnings.length > 0) {
  console.warn('⚠️ Health Goal System Warnings:', validation.warnings);
}

// Router starten
router.resolve();

// Mache appState global verfügbar
window.appState = appStorage._data;

// Event Delegation für Navigation und dynamische Links
document.body.addEventListener("click", (event) => {
  let target = event.target;

  // Prüfen, ob der Footer-Zurück-Button geklickt wurde
  if (target.id === "footer-back-button" || target.closest("#footer-back-button")) {
    event.preventDefault();
    window.history.back();
    return;
  }

  // Suche nach dem übergeordneten Element mit 'data-navigate', falls das aktuelle Maus-Target keins ist (img im div container etc.)
  while (target && !target.hasAttribute("data-navigate")) {
    target = target.parentElement; // Gehe im DOM nach oben
  }

  // Falls ein Element mit 'data-navigate' gefunden wurde, handle die Navigation
  if (target && target.hasAttribute("data-navigate")) {
    event.preventDefault();
    const path = target.getAttribute("data-navigate");
    
    // Wenn Home-Button geklickt wird (Navigation zur Startseite), setze den AppStorage zurück
    if (path === "/" && target.closest(".button-home")) {
      console.log("🏠 Home-Button geklickt, führe vollständigen Reset durch");
      const resetResult = resetHealthGoalProgress(true);

      if (resetResult.success) {
        console.log("✅ Reset erfolgreich abgeschlossen");

        // Validiere nach dem Reset
        const postResetValidation = validateHealthGoalSystem();
        if (!postResetValidation.valid) {
          console.error("❌ System nach Reset nicht valide:", postResetValidation.issues);
        } else {
          console.log("✅ System nach Reset validiert");
        }
      } else {
        console.error("❌ Reset fehlgeschlagen:", resetResult.error);
      }
    }
    
    router.navigate(path);
  }
});

console.log("index.js loaded");

// Initialisiere das Drag-Scrolling nur für den inneren Container
initDragScroll('.image-card-narrow-container');

// Event-Listener für dynamisch geladene Inhalte
document.addEventListener('DOMContentLoaded', () => {
  console.log('DOM geladen, initialisiere Drag-Scrolling');
  initDragScroll('.image-card-narrow-container');
});
