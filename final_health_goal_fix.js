/**
 * Final fix for health goal display issue
 * This script addresses the template structure problem
 */

console.log('🔧 === FINAL HEALTH GOAL FIX START ===');

const finalHealthGoalFix = () => {
  console.log('🔧 Running final health goal fix...');
  
  // Step 1: Ensure health goal is active
  console.log('📊 Step 1: Ensuring health goal is active');
  if (!appStorage.activeHealthGoals.fitUmgebung) {
    console.log('⚠️ Activating health goal...');
    appStorage.activateHealthGoal('fitUmgebung');
  } else {
    console.log('✅ Health goal already active');
  }
  
  // Step 2: Force regenerate the template content
  console.log('📊 Step 2: Force regenerating template content');
  
  const segment1 = document.getElementById('Segment1');
  if (segment1) {
    console.log('✅ Found Segment1 element');
    
    // Import the template function and render fresh content
    Promise.all([
      import('./webcomponents/segments/healthgoalOverview_Segment1.js'),
      import('lit-html')
    ]).then(([{ healthgoalOverview_segment1Content }, { render }]) => {
      
      console.log('🔄 Generating fresh template content...');
      
      // Clear existing content first
      segment1.innerHTML = '';
      
      // Generate fresh content
      const freshContent = healthgoalOverview_segment1Content();
      console.log('✅ Generated fresh content:', freshContent);
      
      // Render the fresh content
      render(freshContent, segment1);
      console.log('🎯 Rendered fresh content to Segment1');
      
      // Ensure visibility
      segment1.style.display = 'flex';
      segment1.style.flexDirection = 'column';
      segment1.style.visibility = 'visible';
      segment1.style.opacity = '1';
      
      console.log('👁️ Applied visibility styles');
      
      // Check the result
      setTimeout(() => {
        const hasActiveGoal = segment1.innerHTML.includes('Fit in deiner Umgebung');
        const hasActiveStatus = segment1.innerHTML.includes('Gerade aktiv');
        const hasProgress = segment1.innerHTML.includes('von 4 Challenges');
        
        console.log('📊 Content check after fix:');
        console.log('  - Contains "Fit in deiner Umgebung":', hasActiveGoal);
        console.log('  - Contains "Gerade aktiv":', hasActiveStatus);
        console.log('  - Contains progress text:', hasProgress);
        
        if (hasActiveGoal && hasActiveStatus && hasProgress) {
          console.log('🎉 SUCCESS! Active health goal is now displayed correctly!');
        } else {
          console.log('⚠️ Content generated but may not be fully correct');
          console.log('📄 Current content preview:', segment1.innerHTML.substring(0, 500) + '...');
        }
      }, 500);
      
    }).catch(error => {
      console.error('❌ Error importing modules:', error);
    });
    
  } else {
    console.log('❌ Segment1 element not found');
    
    // Try to find it with alternative methods
    const allSegments = document.querySelectorAll('[id*="Segment"]');
    console.log('🔍 Found segments:', allSegments.length);
    allSegments.forEach((seg, index) => {
      console.log(`  Segment ${index}: ${seg.id}`);
    });
  }
  
  // Step 3: Force update segmented control
  console.log('📊 Step 3: Force updating segmented control');
  
  if (window.segmentedControl && typeof window.segmentedControl.updateContent === 'function') {
    console.log('🔄 Updating segmented control content...');
    window.segmentedControl.updateContent('Segment1');
  } else {
    console.log('⚠️ Segmented control not available for manual update');
  }
  
  // Step 4: Ensure tab is active
  console.log('📊 Step 4: Ensuring tab is active');
  
  const firstTab = document.querySelector('.tablinks');
  if (firstTab) {
    console.log('✅ Found first tab, ensuring it is active');
    
    // Remove active from all tabs
    const allTabs = document.querySelectorAll('.tablinks');
    allTabs.forEach(tab => tab.classList.remove('active'));
    
    // Hide all tab content
    const allTabContent = document.querySelectorAll('.tabcontent');
    allTabContent.forEach(content => content.style.display = 'none');
    
    // Activate first tab
    firstTab.classList.add('active');
    
    // Show first tab content
    if (segment1) {
      segment1.style.display = 'flex';
      segment1.style.flexDirection = 'column';
    }
    
    console.log('✅ Tab activation completed');
  } else {
    console.log('⚠️ No tab buttons found');
  }
};

// Step 5: Create a comprehensive test function
const testHealthGoalDisplay = () => {
  console.log('🧪 Testing health goal display...');
  
  const segment1 = document.getElementById('Segment1');
  if (!segment1) {
    console.log('❌ Segment1 not found');
    return false;
  }
  
  const isVisible = getComputedStyle(segment1).display !== 'none';
  const hasContent = segment1.innerHTML.length > 100;
  const hasActiveGoal = segment1.innerHTML.includes('Fit in deiner Umgebung');
  const hasActiveStatus = segment1.innerHTML.includes('Gerade aktiv');
  const hasProgress = segment1.innerHTML.includes('von 4 Challenges');
  
  console.log('📊 Test results:');
  console.log('  - Segment1 visible:', isVisible);
  console.log('  - Has content:', hasContent);
  console.log('  - Contains active goal:', hasActiveGoal);
  console.log('  - Contains active status:', hasActiveStatus);
  console.log('  - Contains progress:', hasProgress);
  
  const allTestsPassed = isVisible && hasContent && hasActiveGoal && hasActiveStatus && hasProgress;
  
  if (allTestsPassed) {
    console.log('🎉 ALL TESTS PASSED! Health goal display is working correctly!');
  } else {
    console.log('⚠️ Some tests failed. Running fix...');
    finalHealthGoalFix();
  }
  
  return allTestsPassed;
};

// Step 6: Auto-run with retries
const runWithRetries = (maxRetries = 3) => {
  let attempts = 0;
  
  const attempt = () => {
    attempts++;
    console.log(`🔄 Attempt ${attempts}/${maxRetries}`);
    
    const success = testHealthGoalDisplay();
    
    if (!success && attempts < maxRetries) {
      console.log(`⏰ Retrying in 2 seconds... (${attempts}/${maxRetries})`);
      setTimeout(attempt, 2000);
    } else if (success) {
      console.log('🎉 Fix successful!');
    } else {
      console.log('❌ Fix failed after all attempts');
      console.log('💡 Try running window.finalHealthGoalFix.fix() manually');
    }
  };
  
  attempt();
};

// Export functions for manual use
window.finalHealthGoalFix = {
  fix: finalHealthGoalFix,
  test: testHealthGoalDisplay,
  runWithRetries: runWithRetries
};

console.log('🔧 === FINAL HEALTH GOAL FIX READY ===');
console.log('💡 Available functions:');
console.log('  - window.finalHealthGoalFix.fix() - Run the fix');
console.log('  - window.finalHealthGoalFix.test() - Test current state');
console.log('  - window.finalHealthGoalFix.runWithRetries() - Auto-fix with retries');

// Auto-run the fix with retries
console.log('🚀 Auto-running fix with retries...');
runWithRetries();
