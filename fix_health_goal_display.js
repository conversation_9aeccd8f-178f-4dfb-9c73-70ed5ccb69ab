/**
 * Fix script for health goal display issue
 * Run this in the browser console to fix the issue
 */

console.log('🔧 === HEALTH GOAL DISPLAY FIX START ===');

// Function to force update the health goal display
const forceUpdateHealthGoalDisplay = () => {
  console.log('🔄 Force updating health goal display...');
  
  // Step 1: Ensure health goal is active
  if (!appStorage.activeHealthGoals.fitUmgebung) {
    console.log('⚠️ Activating health goal...');
    appStorage.activateHealthGoal('fitUmgebung');
  }
  
  // Step 2: Find and update the segment content
  const segment1 = document.getElementById('Segment1');
  if (segment1) {
    console.log('✅ Found Segment1 element');
    
    // Import required modules and update content
    Promise.all([
      import('./webcomponents/segments/healthgoalOverview_Segment1.js'),
      import('lit-html')
    ]).then(([{ healthgoalOverview_segment1Content }, { render }]) => {
      console.log('🔄 Updating Segment1 content with fresh template...');
      
      // Generate fresh content
      const newContent = healthgoalOverview_segment1Content();
      console.log('✅ Generated fresh content:', newContent);
      
      // Render the new content
      render(newContent, segment1);
      console.log('🎯 Content rendered to Segment1');
      
      // Ensure it's visible
      segment1.style.display = 'flex';
      segment1.style.flexDirection = 'column';
      console.log('👁️ Made Segment1 visible');
      
    }).catch(error => {
      console.error('❌ Error updating content:', error);
    });
  } else {
    console.warn('⚠️ Segment1 element not found');
  }
};

// Function to initialize segmented control properly
const initializeSegmentedControl = () => {
  console.log('🚀 Initializing segmented control...');
  
  // Find the first tab button
  const firstTab = document.querySelector('.tablinks');
  if (firstTab) {
    console.log('✅ Found first tab, activating...');
    
    // Hide all tab content first
    const allTabContent = document.querySelectorAll('.tabcontent');
    allTabContent.forEach(content => {
      content.style.display = 'none';
    });
    
    // Remove active class from all tabs
    const allTabs = document.querySelectorAll('.tablinks');
    allTabs.forEach(tab => {
      tab.classList.remove('active');
    });
    
    // Activate the first tab
    firstTab.classList.add('active');
    
    // Show the first tab content
    const firstTabContent = document.getElementById('Segment1');
    if (firstTabContent) {
      firstTabContent.style.display = 'flex';
      firstTabContent.style.flexDirection = 'column';
      console.log('✅ Activated first tab content');
    }
    
    // Force update the content
    setTimeout(() => forceUpdateHealthGoalDisplay(), 100);
    
  } else {
    console.warn('⚠️ No tab buttons found');
  }
};

// Function to check and fix the health goal display
const checkAndFixHealthGoalDisplay = () => {
  console.log('🔍 Checking health goal display...');
  
  // Check if health goal is active
  const isActive = appStorage.activeHealthGoals.fitUmgebung;
  console.log('📊 Health goal active:', isActive);
  
  if (!isActive) {
    console.log('⚠️ Health goal not active, activating...');
    appStorage.activateHealthGoal('fitUmgebung');
  }
  
  // Check if segmented control exists
  const segmentedControlContainer = document.getElementById('segmentedControlContainer');
  console.log('📊 Segmented control container:', !!segmentedControlContainer);
  
  if (segmentedControlContainer) {
    // Check if tabs exist
    const tabs = segmentedControlContainer.querySelectorAll('.tablinks');
    console.log('📊 Number of tabs found:', tabs.length);
    
    // Check if tab content exists
    const tabContent = segmentedControlContainer.querySelectorAll('.tabcontent');
    console.log('📊 Number of tab content areas found:', tabContent.length);
    
    // Check if Segment1 exists and is visible
    const segment1 = document.getElementById('Segment1');
    if (segment1) {
      const isVisible = getComputedStyle(segment1).display !== 'none';
      console.log('📊 Segment1 visible:', isVisible);
      
      if (!isVisible) {
        console.log('⚠️ Segment1 not visible, fixing...');
        initializeSegmentedControl();
      } else {
        console.log('✅ Segment1 is visible, checking content...');
        
        // Check if content contains active health goal
        const hasActiveGoalContent = segment1.innerHTML.includes('Fit in deiner Umgebung') && 
                                   segment1.innerHTML.includes('Gerade aktiv');
        
        if (!hasActiveGoalContent) {
          console.log('⚠️ Segment1 does not contain active health goal, updating...');
          forceUpdateHealthGoalDisplay();
        } else {
          console.log('✅ Segment1 contains active health goal content');
        }
      }
    } else {
      console.warn('⚠️ Segment1 element not found');
    }
  } else {
    console.warn('⚠️ Segmented control container not found');
  }
};

// Main fix function
const fixHealthGoalDisplay = () => {
  console.log('🔧 Starting health goal display fix...');
  
  // Wait for DOM to be ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(checkAndFixHealthGoalDisplay, 500);
    });
  } else {
    setTimeout(checkAndFixHealthGoalDisplay, 500);
  }
  
  // Also try after additional delays
  setTimeout(checkAndFixHealthGoalDisplay, 1000);
  setTimeout(checkAndFixHealthGoalDisplay, 2000);
};

// Export functions for manual use
window.healthGoalDisplayFix = {
  fix: fixHealthGoalDisplay,
  forceUpdate: forceUpdateHealthGoalDisplay,
  initializeControl: initializeSegmentedControl,
  check: checkAndFixHealthGoalDisplay
};

console.log('🔧 === HEALTH GOAL DISPLAY FIX READY ===');
console.log('💡 Available functions:');
console.log('  - window.healthGoalDisplayFix.fix() - Run complete fix');
console.log('  - window.healthGoalDisplayFix.forceUpdate() - Force update content');
console.log('  - window.healthGoalDisplayFix.initializeControl() - Initialize segmented control');
console.log('  - window.healthGoalDisplayFix.check() - Check current state');

// Auto-run the fix
fixHealthGoalDisplay();
