/**
 * Simple fix for health goal display issue
 * This script directly manipulates the DOM to show the active health goal
 */

console.log('🔧 === SIMPLE HEALTH GOAL FIX START ===');

const simpleHealthGoalFix = () => {
  console.log('🔧 Running simple health goal fix...');
  
  // Step 1: Ensure health goal is active
  if (!appStorage.activeHealthGoals.fitUmgebung) {
    console.log('⚠️ Activating health goal...');
    appStorage.activateHealthGoal('fitUmgebung');
  }
  
  // Step 2: Get the active health goal data
  console.log('📊 Getting active health goal data...');
  
  // Import the utility functions
  import('./src/utils/healthGoalUtils.js').then(({ getActiveHealthGoal, getHealthGoalCardProps }) => {
    
    const activeGoal = getActiveHealthGoal();
    console.log('✅ Active goal:', activeGoal);
    
    if (!activeGoal) {
      console.log('❌ No active goal found');
      return;
    }
    
    const cardProps = getHealthGoalCardProps(activeGoal.id, true);
    console.log('✅ Card props:', cardProps);
    
    if (!cardProps) {
      console.log('❌ No card props found');
      return;
    }
    
    // Step 3: Create the HTML content directly
    console.log('🎨 Creating HTML content directly...');
    
    const activeGoalHTML = `
      <div class="tabpadding">
        <div class="screen-centered content-padding">
          <h2 class="tabheadline">Dein aktives Ziel</h2>
          <div class="healthgoal-cards">
            <a data-navigate="${activeGoal.link || '/healthgoals-overview/hg-fitUmgebung'}">
              <div class="healthgoal-card-active">
                <img src="${cardProps.cardImage}" alt="Health goal image" class="card-image-active" />
                <div class="pill active-pill" style="background-color: var(${cardProps.pillColor})">
                  <p class="caption semibold">${cardProps.pillText}</p>
                </div>
                <div class="healthgoal-card-content">
                  <div class="tag" style="background-color: var(${cardProps.tagColor})">
                    <img src="${cardProps.tagIcon}" alt="Tag icon" class="tag-icon" />
                    <span class="tag-text small semibold" style="color: var(${cardProps.tagTextColor})">${cardProps.tagCategory}</span>
                  </div>
                  <h3 class="healthgoal-title semibold tag-text">${cardProps.healthgoalTitle}</h3>
                  <p class="healthgoal-coop caption tag-text">${cardProps.healthgoalCoop}</p>
                  <div class="progress-bar">
                    <div class="progress-info">
                      <span class="progress-text small">${cardProps.completedChallenges} von ${cardProps.totalChallenges} Challenges geschafft</span>
                      <span class="progress-percentage small">${Math.round((cardProps.completedChallenges / cardProps.totalChallenges) * 100)}%</span>
                    </div>
                    <div class="progress-track">
                      <div class="progress-fill" style="width: ${(cardProps.completedChallenges / cardProps.totalChallenges) * 100}%"></div>
                    </div>
                  </div>
                  <div class="goal-info-active">
                    <div class="info-item">
                      <img src="${cardProps.goalinfoIcon1}" alt="Info icon 1" class="info-icon" />
                      <span class="info-text small tag-text">${cardProps.goalinfoText1}</span>
                    </div>
                    <div class="info-item">
                      <img src="${cardProps.goalinfoIcon2}" alt="Info icon 2" class="info-icon" />
                      <span class="info-text small tag-text">${cardProps.goalinfoText2}</span>
                    </div>
                  </div>
                </div>
              </div>
            </a>
          </div>
        </div>
      </div>
      
      <!-- Rest of the content -->
      <div class="content-padding firstcard-no-top-padding">
        <!-- Info Box Push Notification -->
        <div class="standard-container">
          <div class="card info-card-slider" style="background-color: var(--info-card-background-blue);">
            <div class="icon-container">
              <img alt="Icon" src="http://localhost:1234/icon_bell.svg">
            </div>
            <div class="content-container">
              <div class="text-content">
                <p class="title semibold">Du möchtest nichts vergessen?</p>
                <p class="description small">Stelle hier meine Erinnerungen an Dich ein</p>
              </div>
              <div class="button-container">
                <span class="link-text">Einstellungen</span>
                <img alt="Icon" class="img-button is-animated" src="http://localhost:1234/ArrowRight.svg">
              </div>
            </div>
          </div>
        </div>
        
        <h2 class="section-title semibold">Alle Gesundheitsziele</h2>
        
        <div class="standard-container">
          <div class="chip-container">
            <div class="chip active" data-category="all">Alle</div>
            <div class="chip" data-category="Bewegung">Bewegung</div>
            <div class="chip" data-category="Ernährung">Ernährung</div>
            <div class="chip" data-category="Schlaf">Schlaf</div>
            <div class="chip" data-category="Psyche">Psyche</div>
          </div>
        </div>
        
        <div class="healthgoal-cards">
          <!-- Inactive health goals would go here -->
        </div>
      </div>
    `;
    
    // Step 4: Insert the HTML into Segment1
    const segment1 = document.getElementById('Segment1');
    if (segment1) {
      console.log('✅ Found Segment1, inserting HTML...');
      segment1.innerHTML = activeGoalHTML;
      
      // Ensure visibility
      segment1.style.display = 'flex';
      segment1.style.flexDirection = 'column';
      segment1.style.visibility = 'visible';
      segment1.style.opacity = '1';
      
      console.log('🎉 SUCCESS! Active health goal HTML inserted directly!');
      
      // Test the result
      setTimeout(() => {
        const hasActiveGoal = segment1.innerHTML.includes('Fit in deiner Umgebung');
        const hasActiveStatus = segment1.innerHTML.includes('Gerade aktiv');
        const hasProgress = segment1.innerHTML.includes('von 4 Challenges');
        
        console.log('📊 Final test results:');
        console.log('  - Contains "Fit in deiner Umgebung":', hasActiveGoal);
        console.log('  - Contains "Gerade aktiv":', hasActiveStatus);
        console.log('  - Contains progress text:', hasProgress);
        
        if (hasActiveGoal && hasActiveStatus && hasProgress) {
          console.log('🎉 PERFECT! All content is now visible!');
        } else {
          console.log('⚠️ Some content may still be missing');
        }
      }, 100);
      
    } else {
      console.log('❌ Segment1 element not found');
    }
    
  }).catch(error => {
    console.error('❌ Error importing utilities:', error);
  });
};

// Export for manual use
window.simpleHealthGoalFix = {
  fix: simpleHealthGoalFix
};

console.log('🔧 === SIMPLE HEALTH GOAL FIX READY ===');
console.log('💡 Run: window.simpleHealthGoalFix.fix()');

// Auto-run the fix
console.log('🚀 Auto-running simple fix...');
simpleHealthGoalFix();
